/* Enhanced DeFi Dynasty Styles */
/* Advanced animations, particle effects, and premium UI components */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    background: linear-gradient(135deg, #0a0a1a 0%, #1a1a3e 25%, #2d1b69 50%, #4a2c7a 75%, #6b3d8c 100%);
    color: #ffffff;
    overflow: hidden;
    height: 100vh;
    position: relative;
}

/* Particle Background */
#particles-js {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

#game-container {
    width: 100vw;
    height: 100vh;
    position: relative;
    z-index: 1;
}

/* Enhanced Screen Management */
.screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transform: scale(0.95);
}

.screen.active {
    opacity: 1;
    visibility: visible;
    transform: scale(1);
}

/* Enhanced Loading Screen */
#loading-screen {
    background: radial-gradient(circle at center, rgba(26, 26, 62, 0.9) 0%, rgba(10, 10, 26, 0.95) 100%);
    backdrop-filter: blur(10px);
}

.loading-content {
    text-align: center;
    max-width: 500px;
    padding: 2rem;
}

.logo-container {
    margin-bottom: 2rem;
}

.game-title {
    font-family: 'Orbitron', monospace;
    font-size: 4rem;
    font-weight: 900;
    background: linear-gradient(45deg, #00d4ff, #ff6b6b, #4ecdc4, #ffe66d);
    background-size: 400% 400%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradientShift 4s ease infinite, titlePulse 2s ease-in-out infinite alternate;
    margin-bottom: 1rem;
    text-shadow: 0 0 30px rgba(0, 212, 255, 0.5);
}

.logo-animation {
    position: relative;
    height: 80px;
    margin: 1rem 0;
}

.blockchain-nodes {
    position: relative;
    width: 200px;
    height: 60px;
    margin: 0 auto;
}

.node {
    position: absolute;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: linear-gradient(45deg, #00d4ff, #4ecdc4);
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.8);
    animation: nodePulse 2s ease-in-out infinite;
}

.node-1 { left: 0; top: 20px; animation-delay: 0s; }
.node-2 { left: 90px; top: 0; animation-delay: 0.5s; }
.node-3 { right: 0; top: 20px; animation-delay: 1s; }

.connection {
    position: absolute;
    height: 2px;
    background: linear-gradient(90deg, transparent, #00d4ff, transparent);
    animation: connectionFlow 3s ease-in-out infinite;
}

.connection-1 {
    left: 20px;
    top: 30px;
    width: 70px;
    transform: rotate(-15deg);
}

.connection-2 {
    right: 20px;
    top: 30px;
    width: 70px;
    transform: rotate(15deg);
}

.game-subtitle {
    font-size: 1.4rem;
    color: #a0a0a0;
    margin-bottom: 3rem;
    animation: subtitleGlow 3s ease-in-out infinite alternate;
}

.loading-bar-container {
    position: relative;
    margin-bottom: 2rem;
}

.loading-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
    position: relative;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
}

.loading-progress {
    height: 100%;
    background: linear-gradient(90deg, #00d4ff, #4ecdc4, #ffe66d);
    width: 0%;
    animation: loadingProgress 4s ease-in-out forwards;
    position: relative;
}

.loading-glow {
    position: absolute;
    top: -2px;
    left: -10px;
    right: -10px;
    bottom: -2px;
    background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.5), transparent);
    border-radius: 6px;
    animation: glowSweep 2s ease-in-out infinite;
}

.loading-percentage {
    position: absolute;
    top: -30px;
    right: 0;
    font-size: 0.9rem;
    color: #00d4ff;
    font-weight: 600;
}

.loading-text {
    color: #a0a0a0;
    font-size: 1rem;
    margin-bottom: 1rem;
    animation: textFade 2s ease-in-out infinite alternate;
}

.loading-tips {
    margin-top: 2rem;
}

.tip-text {
    color: #4ecdc4;
    font-size: 0.9rem;
    animation: tipSlide 3s ease-in-out infinite;
}

/* Enhanced Main Menu */
.menu-content {
    text-align: center;
    max-width: 600px;
    padding: 2rem;
}

.menu-header {
    position: relative;
    margin-bottom: 2rem;
}

.version-badge {
    position: absolute;
    top: -10px;
    right: -20px;
    background: linear-gradient(45deg, #ff6b6b, #ee5a52);
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.7rem;
    font-weight: 600;
    animation: badgeBounce 2s ease-in-out infinite;
}

.menu-stats {
    display: flex;
    justify-content: space-around;
    margin-bottom: 3rem;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.stat-item {
    text-align: center;
    flex: 1;
}

.stat-icon {
    font-size: 1.5rem;
    display: block;
    margin-bottom: 0.5rem;
    animation: iconFloat 3s ease-in-out infinite;
}

.stat-label {
    display: block;
    font-size: 0.8rem;
    color: #a0a0a0;
    margin-bottom: 0.3rem;
}

.stat-value {
    display: block;
    font-size: 1.2rem;
    font-weight: 700;
    color: #00d4ff;
}

.menu-buttons {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.menu-btn {
    position: relative;
    padding: 1.2rem 2rem;
    border: none;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    text-transform: uppercase;
    letter-spacing: 1px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.8rem;
    overflow: hidden;
}

.menu-btn.primary {
    background: linear-gradient(45deg, #00d4ff, #4ecdc4);
    color: #1a1a3e;
    box-shadow: 0 8px 25px rgba(0, 212, 255, 0.3);
}

.menu-btn.secondary {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 15px rgba(255, 255, 255, 0.1);
}

.menu-btn:hover {
    transform: translateY(-3px) scale(1.02);
}

.menu-btn.primary:hover {
    box-shadow: 0 12px 35px rgba(0, 212, 255, 0.5);
}

.menu-btn.secondary:hover {
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);
}

.btn-icon {
    font-size: 1.3rem;
    animation: iconBounce 2s ease-in-out infinite;
}

.btn-text {
    position: relative;
    z-index: 2;
}

.btn-glow {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.menu-btn:hover .btn-glow {
    left: 100%;
}

.menu-footer {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.icon-btn {
    width: 50px;
    height: 50px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.icon-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1) rotate(10deg);
}

/* Animations */
@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes titlePulse {
    0% { transform: scale(1); }
    100% { transform: scale(1.05); }
}

@keyframes nodePulse {
    0%, 100% { transform: scale(1); box-shadow: 0 0 20px rgba(0, 212, 255, 0.8); }
    50% { transform: scale(1.2); box-shadow: 0 0 30px rgba(0, 212, 255, 1); }
}

@keyframes connectionFlow {
    0% { opacity: 0.3; }
    50% { opacity: 1; }
    100% { opacity: 0.3; }
}

@keyframes subtitleGlow {
    0% { text-shadow: 0 0 10px rgba(160, 160, 160, 0.5); }
    100% { text-shadow: 0 0 20px rgba(160, 160, 160, 0.8); }
}

@keyframes loadingProgress {
    0% { width: 0%; }
    100% { width: 100%; }
}

@keyframes glowSweep {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

@keyframes textFade {
    0% { opacity: 0.7; }
    100% { opacity: 1; }
}

@keyframes tipSlide {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-5px); }
}

@keyframes badgeBounce {
    0%, 100% { transform: scale(1) rotate(-5deg); }
    50% { transform: scale(1.1) rotate(5deg); }
}

@keyframes iconFloat {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-5px); }
}

@keyframes iconBounce {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .game-title {
        font-size: 2.5rem;
    }
    
    .menu-stats {
        flex-direction: column;
        gap: 1rem;
    }
    
    .menu-btn {
        padding: 1rem 1.5rem;
        font-size: 1rem;
    }
}

/* Enhanced Game HUD */
.hud {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    pointer-events: none;
}

.hud.hidden {
    display: none;
}

.hud > * {
    pointer-events: auto;
}

.hud-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: linear-gradient(135deg, rgba(15, 15, 35, 0.95), rgba(26, 26, 62, 0.9));
    backdrop-filter: blur(15px);
    border-bottom: 1px solid rgba(0, 212, 255, 0.3);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.resources {
    display: flex;
    gap: 1.5rem;
}

.resource {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    padding: 0.8rem 1.2rem;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    border-radius: 25px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.resource:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 212, 255, 0.2);
}

.resource-icon-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.resource-icon {
    font-size: 1.4rem;
    z-index: 2;
}

.resource-pulse {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(0, 212, 255, 0.3), transparent);
    animation: resourcePulse 2s ease-in-out infinite;
}

.resource-info {
    display: flex;
    flex-direction: column;
    gap: 0.2rem;
}

.resource-label {
    font-size: 0.8rem;
    color: #a0a0a0;
    font-weight: 500;
}

.resource-value {
    font-weight: 700;
    color: #ffffff;
    font-size: 1.1rem;
}

.resource-bar {
    width: 60px;
    height: 4px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
    overflow: hidden;
    margin-top: 0.2rem;
}

.resource-fill {
    height: 100%;
    border-radius: 2px;
    transition: width 0.5s ease;
}

.eth-fill {
    background: linear-gradient(90deg, #00d4ff, #4ecdc4);
    width: 75%;
}

.token-fill {
    background: linear-gradient(90deg, #ffe66d, #ffed8a);
    width: 45%;
}

.energy-fill {
    background: linear-gradient(90deg, #ff6b6b, #ff8e8e);
    width: 100%;
}

.xp-fill {
    background: linear-gradient(90deg, #a8e6cf, #c4f0db);
    width: 60%;
}

.hud-controls {
    display: flex;
    gap: 0.8rem;
}

.hud-btn {
    position: relative;
    width: 50px;
    height: 50px;
    border: none;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.hud-btn:hover {
    background: linear-gradient(135deg, rgba(0, 212, 255, 0.3), rgba(0, 212, 255, 0.1));
    transform: scale(1.1);
    box-shadow: 0 5px 20px rgba(0, 212, 255, 0.3);
}

.btn-ripple {
    position: absolute;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3), transparent);
    border-radius: 50%;
    transform: scale(0);
    transition: transform 0.3s ease;
}

.hud-btn:active .btn-ripple {
    transform: scale(1);
}

.hud-bottom {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 1rem;
    background: linear-gradient(135deg, rgba(15, 15, 35, 0.95), rgba(26, 26, 62, 0.9));
    backdrop-filter: blur(15px);
    border-top: 1px solid rgba(0, 212, 255, 0.3);
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3);
}

.protocol-tabs {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.protocol-tab {
    position: relative;
    padding: 1rem 1.5rem;
    border: none;
    border-radius: 15px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    color: #a0a0a0;
    cursor: pointer;
    transition: all 0.4s ease;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.protocol-tab.active {
    background: linear-gradient(45deg, #00d4ff, #4ecdc4);
    color: #1a1a3e;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 212, 255, 0.4);
}

.protocol-tab:hover:not(.active) {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
    color: #ffffff;
    transform: translateY(-2px);
}

.tab-icon {
    font-size: 1.2rem;
}

.tab-label {
    font-size: 0.9rem;
}

.tab-glow {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.protocol-tab:hover .tab-glow {
    left: 100%;
}

/* Enhanced Game World */
#game-world {
    background: radial-gradient(ellipse at center, rgba(26, 26, 62, 0.3) 0%, rgba(10, 10, 26, 0.8) 100%);
}

.world-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
}

#phaser-game-container {
    width: 100%;
    height: 100%;
    position: relative;
}

#game-canvas {
    width: 100%;
    height: 100%;
    border-radius: 10px;
}

.world-info-panel {
    position: absolute;
    top: 100px;
    left: 20px;
    background: linear-gradient(135deg, rgba(15, 15, 35, 0.9), rgba(26, 26, 62, 0.8));
    backdrop-filter: blur(15px);
    border-radius: 15px;
    padding: 1.5rem;
    border: 1px solid rgba(0, 212, 255, 0.3);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    max-width: 300px;
}

.world-header {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.world-icon {
    font-size: 2rem;
    animation: worldIconSpin 10s linear infinite;
}

.world-details h3 {
    font-family: 'Orbitron', monospace;
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
    color: #00d4ff;
}

.world-details p {
    color: #a0a0a0;
    font-size: 0.9rem;
    line-height: 1.4;
}

.world-stats {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.stat {
    text-align: center;
}

.stat-label {
    display: block;
    font-size: 0.7rem;
    color: #a0a0a0;
    margin-bottom: 0.2rem;
}

.stat-value {
    display: block;
    font-size: 1rem;
    font-weight: 700;
    color: #4ecdc4;
}

/* Building Interface */
.building-interface {
    position: absolute;
    bottom: 120px;
    right: 20px;
    background: linear-gradient(135deg, rgba(15, 15, 35, 0.95), rgba(26, 26, 62, 0.9));
    backdrop-filter: blur(15px);
    border-radius: 15px;
    padding: 1.5rem;
    border: 1px solid rgba(0, 212, 255, 0.3);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    max-width: 320px;
    transform: translateX(100%);
    transition: transform 0.4s ease;
}

.building-interface:not(.hidden) {
    transform: translateX(0);
}

.interface-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.interface-header h4 {
    color: #00d4ff;
    font-family: 'Orbitron', monospace;
}

.close-interface {
    background: none;
    border: none;
    color: #a0a0a0;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.2rem;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-interface:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
}

.protocol-preview {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
}

.preview-icon {
    font-size: 2.5rem;
    animation: previewIconBounce 2s ease-in-out infinite;
}

.preview-info h5 {
    color: #ffffff;
    margin-bottom: 0.5rem;
}

.preview-info p {
    color: #a0a0a0;
    font-size: 0.9rem;
}

.build-costs {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.cost-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.cost-icon {
    font-size: 1.1rem;
}

.cost-value {
    font-size: 0.9rem;
    font-weight: 600;
    color: #ffffff;
}

.build-btn {
    position: relative;
    width: 100%;
    padding: 1rem;
    border: none;
    border-radius: 10px;
    background: linear-gradient(45deg, #00d4ff, #4ecdc4);
    color: #1a1a3e;
    font-weight: 700;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    overflow: hidden;
}

.build-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 212, 255, 0.4);
}

.btn-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.build-btn:hover .btn-particles {
    opacity: 1;
    animation: particleFloat 1s ease-in-out infinite;
}

/* Animations */
@keyframes resourcePulse {
    0%, 100% { opacity: 0.3; transform: scale(1); }
    50% { opacity: 0.6; transform: scale(1.1); }
}

@keyframes worldIconSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes previewIconBounce {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes particleFloat {
    0%, 100% { transform: translateY(0) scale(1); }
    50% { transform: translateY(-5px) scale(1.05); }
}

/* Achievement System */
.achievement-popup {
    position: fixed;
    top: 50px;
    right: 50px;
    background: linear-gradient(135deg, rgba(15, 15, 35, 0.95), rgba(26, 26, 62, 0.9));
    backdrop-filter: blur(15px);
    border-radius: 15px;
    padding: 1.5rem;
    border: 2px solid #ffd700;
    box-shadow: 0 10px 30px rgba(255, 215, 0, 0.3);
    z-index: 10003;
    transform: translateY(-100px) scale(0.8);
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    max-width: 350px;
}

.achievement-popup:not(.hidden) {
    transform: translateY(0) scale(1);
    opacity: 1;
}

.achievement-popup.common { border-color: #a0a0a0; }
.achievement-popup.uncommon { border-color: #4ecdc4; }
.achievement-popup.rare { border-color: #00d4ff; }
.achievement-popup.epic { border-color: #ff6b6b; }
.achievement-popup.legendary { border-color: #ffd700; }

.achievement-content {
    display: flex;
    align-items: center;
    gap: 1rem;
    position: relative;
}

.achievement-icon {
    font-size: 3rem;
    animation: achievementBounce 2s ease-in-out infinite;
}

.achievement-info {
    flex: 1;
}

.achievement-title {
    color: #ffd700;
    font-family: 'Orbitron', monospace;
    font-size: 1.2rem;
    margin: 0 0 0.5rem 0;
}

.achievement-description {
    color: #a0a0a0;
    font-size: 0.9rem;
    margin: 0;
    line-height: 1.4;
}

.achievement-glow {
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: radial-gradient(circle, rgba(255, 215, 0, 0.2), transparent);
    border-radius: 20px;
    animation: achievementGlow 2s ease-in-out infinite alternate;
    z-index: -1;
}

/* Daily Challenge */
.daily-challenge {
    position: fixed;
    top: 50%;
    left: 20px;
    transform: translateY(-50%);
    background: linear-gradient(135deg, rgba(15, 15, 35, 0.9), rgba(26, 26, 62, 0.8));
    backdrop-filter: blur(15px);
    border-radius: 15px;
    padding: 1rem;
    border: 1px solid rgba(0, 212, 255, 0.3);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    max-width: 250px;
    z-index: 999;
}

.challenge-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.8rem;
}

.challenge-icon {
    font-size: 1.2rem;
}

.challenge-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: #00d4ff;
}

.challenge-timer {
    font-size: 0.8rem;
    color: #ffe66d;
    font-family: 'Orbitron', monospace;
}

.challenge-progress {
    margin-top: 0.5rem;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #00d4ff, #4ecdc4);
    border-radius: 3px;
    transition: width 0.5s ease;
    width: 0%;
}

.progress-text {
    font-size: 0.8rem;
    color: #a0a0a0;
    line-height: 1.3;
}

/* Enhanced Notifications */
.enhanced-notification {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    border-radius: 12px;
    color: #ffffff;
    font-weight: 500;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    margin-bottom: 0.5rem;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    max-width: 400px;
}

.enhanced-notification.success {
    background: linear-gradient(135deg, rgba(78, 205, 196, 0.9), rgba(68, 160, 141, 0.8));
    border-color: rgba(78, 205, 196, 0.5);
}

.enhanced-notification.error {
    background: linear-gradient(135deg, rgba(255, 107, 107, 0.9), rgba(238, 90, 82, 0.8));
    border-color: rgba(255, 107, 107, 0.5);
}

.enhanced-notification.info {
    background: linear-gradient(135deg, rgba(0, 212, 255, 0.9), rgba(0, 153, 204, 0.8));
    border-color: rgba(0, 212, 255, 0.5);
}

.enhanced-notification.achievement {
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.9), rgba(204, 172, 0, 0.8));
    border-color: rgba(255, 215, 0, 0.5);
}

.notification-icon {
    font-size: 1.5rem;
    min-width: 24px;
}

.notification-content {
    flex: 1;
}

.notification-message {
    font-size: 1rem;
    margin-bottom: 0.2rem;
}

.notification-subtitle {
    font-size: 0.8rem;
    opacity: 0.8;
}

.notification-close {
    background: none;
    border: none;
    color: currentColor;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.2rem;
    border-radius: 50%;
    transition: background 0.3s ease;
    min-width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.notification-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: rgba(255, 255, 255, 0.5);
    width: 100%;
}

/* Enhanced Tooltips */
.enhanced-tooltip {
    position: fixed;
    background: linear-gradient(135deg, rgba(15, 15, 35, 0.95), rgba(26, 26, 62, 0.9));
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    padding: 1rem;
    z-index: 10004;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s ease;
    max-width: 300px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.tooltip-content {
    color: #ffffff;
}

.tooltip-title {
    font-weight: 600;
    color: #00d4ff;
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.tooltip-description {
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 0.5rem;
    color: #a0a0a0;
}

.tooltip-stats {
    font-size: 0.8rem;
    color: #4ecdc4;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 0.5rem;
}

.tooltip-arrow {
    position: absolute;
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 6px solid rgba(26, 26, 62, 0.9);
    bottom: -6px;
    left: 50%;
    transform: translateX(-50%);
}

/* Floating Texts */
.floating-texts {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 10002;
}

.floating-text {
    position: absolute;
    font-weight: bold;
    text-shadow: 0 0 10px currentColor;
    animation: floatUp 2s ease-out forwards;
    pointer-events: none;
}

.floating-text.resource-gain {
    color: #00d4ff;
    font-size: 1rem;
}

.floating-text.level-up-main {
    color: #ffd700;
    font-size: 1.8rem;
    text-shadow: 0 0 20px currentColor;
}

.floating-text.level-up-number {
    color: #00d4ff;
    font-size: 1.2rem;
}

.floating-text.protocol-earning {
    color: #4ecdc4;
    font-size: 0.9rem;
}

/* Enhanced Animations */
@keyframes achievementBounce {
    0%, 100% { transform: scale(1) rotate(0deg); }
    25% { transform: scale(1.1) rotate(-5deg); }
    75% { transform: scale(1.1) rotate(5deg); }
}

@keyframes achievementGlow {
    0% { opacity: 0.2; transform: scale(1); }
    100% { opacity: 0.4; transform: scale(1.05); }
}

@keyframes floatUp {
    0% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
    100% {
        opacity: 0;
        transform: translateY(-100px) scale(0.8);
    }
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .achievement-popup {
        top: 20px;
        right: 20px;
        left: 20px;
        max-width: none;
    }

    .daily-challenge {
        left: 10px;
        right: 10px;
        max-width: none;
        position: fixed;
        top: auto;
        bottom: 150px;
        transform: none;
    }

    .enhanced-notification {
        margin: 0 10px 0.5rem 10px;
        max-width: none;
    }

    .enhanced-tooltip {
        max-width: 250px;
    }
}

/* Hidden utility */
.hidden {
    display: none !important;
}
