<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeFi Dynasty - Build Your Blockchain Empire</title>
    <link rel="stylesheet" href="styles/enhanced.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">

    <!-- Phaser.js Game Engine -->
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>

    <!-- Particle.js for Background Effects -->
    <script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>

    <!-- Howler.js for Audio -->
    <script src="https://cdn.jsdelivr.net/npm/howler@2.2.3/dist/howler.min.js"></script>
</head>
<body>
    <!-- Particle Background -->
    <div id="particles-js"></div>

    <div id="game-container">
        <!-- Enhanced Loading Screen -->
        <div id="loading-screen" class="screen active">
            <div class="loading-content">
                <div class="logo-container">
                    <h1 class="game-title">DeFi Dynasty</h1>
                    <div class="logo-animation">
                        <div class="blockchain-nodes">
                            <div class="node node-1"></div>
                            <div class="node node-2"></div>
                            <div class="node node-3"></div>
                            <div class="connection connection-1"></div>
                            <div class="connection connection-2"></div>
                        </div>
                    </div>
                </div>
                <p class="game-subtitle">Build Your Blockchain Empire</p>
                <div class="loading-bar-container">
                    <div class="loading-bar">
                        <div class="loading-progress"></div>
                        <div class="loading-glow"></div>
                    </div>
                    <div class="loading-percentage">0%</div>
                </div>
                <p class="loading-text">Initializing blockchain...</p>
                <div class="loading-tips">
                    <p class="tip-text">💡 Tip: Start with AMM protocols for steady income</p>
                </div>
            </div>
        </div>

        <!-- Enhanced Main Menu -->
        <div id="main-menu" class="screen">
            <div class="menu-content">
                <div class="menu-header">
                    <h1 class="game-title">DeFi Dynasty</h1>
                    <div class="version-badge">v2.0 Enhanced</div>
                </div>
                <div class="menu-stats">
                    <div class="stat-item">
                        <span class="stat-icon">👑</span>
                        <span class="stat-label">Best Level</span>
                        <span id="best-level" class="stat-value">1</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-icon">💰</span>
                        <span class="stat-label">Total ETH</span>
                        <span id="total-eth" class="stat-value">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-icon">🏗️</span>
                        <span class="stat-label">Protocols Built</span>
                        <span id="total-protocols" class="stat-value">0</span>
                    </div>
                </div>
                <div class="menu-buttons">
                    <button id="new-game-btn" class="menu-btn primary">
                        <span class="btn-icon">🚀</span>
                        <span class="btn-text">New Empire</span>
                        <div class="btn-glow"></div>
                    </button>
                    <button id="continue-btn" class="menu-btn secondary">
                        <span class="btn-icon">▶️</span>
                        <span class="btn-text">Continue</span>
                        <div class="btn-glow"></div>
                    </button>
                    <button id="tutorial-btn" class="menu-btn secondary">
                        <span class="btn-icon">🎓</span>
                        <span class="btn-text">Tutorial</span>
                        <div class="btn-glow"></div>
                    </button>
                    <button id="leaderboard-btn" class="menu-btn secondary">
                        <span class="btn-icon">🏆</span>
                        <span class="btn-text">Leaderboard</span>
                        <div class="btn-glow"></div>
                    </button>
                    <button id="achievements-btn" class="menu-btn secondary">
                        <span class="btn-icon">🎖️</span>
                        <span class="btn-text">Achievements</span>
                        <div class="btn-glow"></div>
                    </button>
                </div>
                <div class="menu-footer">
                    <button id="settings-menu-btn" class="icon-btn">⚙️</button>
                    <button id="sound-toggle-btn" class="icon-btn">🔊</button>
                    <button id="fullscreen-btn" class="icon-btn">⛶</button>
                </div>
            </div>
        </div>

        <!-- Enhanced Game HUD -->
        <div id="game-hud" class="hud hidden">
            <div class="hud-top">
                <div class="resources">
                    <div class="resource eth-resource">
                        <div class="resource-icon-container">
                            <span class="resource-icon">💎</span>
                            <div class="resource-pulse"></div>
                        </div>
                        <div class="resource-info">
                            <span class="resource-label">ETH</span>
                            <span id="eth-balance" class="resource-value">1000</span>
                            <div class="resource-bar">
                                <div class="resource-fill eth-fill"></div>
                            </div>
                        </div>
                    </div>
                    <div class="resource token-resource">
                        <div class="resource-icon-container">
                            <span class="resource-icon">🪙</span>
                            <div class="resource-pulse"></div>
                        </div>
                        <div class="resource-info">
                            <span class="resource-label">Tokens</span>
                            <span id="token-balance" class="resource-value">0</span>
                            <div class="resource-bar">
                                <div class="resource-fill token-fill"></div>
                            </div>
                        </div>
                    </div>
                    <div class="resource energy-resource">
                        <div class="resource-icon-container">
                            <span class="resource-icon">⚡</span>
                            <div class="resource-pulse"></div>
                        </div>
                        <div class="resource-info">
                            <span class="resource-label">Energy</span>
                            <span id="energy-balance" class="resource-value">100/100</span>
                            <div class="resource-bar">
                                <div id="energy-fill" class="resource-fill energy-fill"></div>
                            </div>
                        </div>
                    </div>
                    <div class="resource level-resource">
                        <div class="resource-icon-container">
                            <span class="resource-icon">🏆</span>
                            <div class="resource-pulse"></div>
                        </div>
                        <div class="resource-info">
                            <span class="resource-label">Level</span>
                            <span id="player-level" class="resource-value">1</span>
                            <div class="resource-bar">
                                <div id="xp-fill" class="resource-fill xp-fill"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="hud-controls">
                    <button id="pause-btn" class="hud-btn" data-tooltip="Pause Game">
                        <span class="btn-icon">⏸️</span>
                        <div class="btn-ripple"></div>
                    </button>
                    <button id="settings-btn" class="hud-btn" data-tooltip="Settings">
                        <span class="btn-icon">⚙️</span>
                        <div class="btn-ripple"></div>
                    </button>
                    <button id="shop-btn" class="hud-btn" data-tooltip="Shop">
                        <span class="btn-icon">🛒</span>
                        <div class="btn-ripple"></div>
                    </button>
                    <button id="achievements-hud-btn" class="hud-btn" data-tooltip="Achievements">
                        <span class="btn-icon">🎖️</span>
                        <div class="btn-ripple"></div>
                    </button>
                </div>
            </div>
            <div class="hud-bottom">
                <div class="protocol-tabs">
                    <button class="protocol-tab active" data-protocol="amm" data-tooltip="Automated Market Maker">
                        <span class="tab-icon">🔄</span>
                        <span class="tab-label">AMM</span>
                        <div class="tab-glow"></div>
                    </button>
                    <button class="protocol-tab" data-protocol="lending" data-tooltip="Lending Protocol">
                        <span class="tab-icon">🏦</span>
                        <span class="tab-label">Lending</span>
                        <div class="tab-glow"></div>
                    </button>
                    <button class="protocol-tab" data-protocol="staking" data-tooltip="Staking Pool">
                        <span class="tab-icon">⚡</span>
                        <span class="tab-label">Staking</span>
                        <div class="tab-glow"></div>
                    </button>
                    <button class="protocol-tab" data-protocol="governance" data-tooltip="DAO Governance">
                        <span class="tab-icon">🏛️</span>
                        <span class="tab-label">DAO</span>
                        <div class="tab-glow"></div>
                    </button>
                </div>
            </div>
        </div>

        <!-- Enhanced Game World -->
        <div id="game-world" class="screen">
            <div class="world-container">
                <!-- Phaser Game Canvas -->
                <div id="phaser-game-container">
                    <canvas id="game-canvas"></canvas>
                </div>

                <!-- World Information Panel -->
                <div class="world-info-panel">
                    <div class="world-header">
                        <div class="world-icon">🌐</div>
                        <div class="world-details">
                            <h3 id="world-name">Ethereum Mainnet</h3>
                            <p id="world-description">The original blockchain where your DeFi empire begins</p>
                        </div>
                        <div class="world-stats">
                            <div class="stat">
                                <span class="stat-label">TVL</span>
                                <span id="world-tvl" class="stat-value">$0</span>
                            </div>
                            <div class="stat">
                                <span class="stat-label">APY</span>
                                <span id="world-apy" class="stat-value">0%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Protocol Building Interface -->
                <div id="building-interface" class="building-interface hidden">
                    <div class="interface-header">
                        <h4>Build Protocol</h4>
                        <button class="close-interface">×</button>
                    </div>
                    <div class="protocol-preview">
                        <div id="preview-icon" class="preview-icon">🔄</div>
                        <div class="preview-info">
                            <h5 id="preview-name">Automated Market Maker</h5>
                            <p id="preview-description">Provides liquidity for token swaps</p>
                        </div>
                    </div>
                    <div class="build-costs">
                        <div class="cost-item">
                            <span class="cost-icon">💎</span>
                            <span id="cost-eth" class="cost-value">100 ETH</span>
                        </div>
                        <div class="cost-item">
                            <span class="cost-icon">⚡</span>
                            <span id="cost-energy" class="cost-value">10 Energy</span>
                        </div>
                    </div>
                    <button id="confirm-build" class="build-btn">
                        <span class="btn-text">Build Protocol</span>
                        <div class="btn-particles"></div>
                    </button>
                </div>
            </div>
        </div>

        <!-- Shop Modal -->
        <div id="shop-modal" class="modal hidden">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>DeFi Shop</h2>
                    <button class="close-btn">&times;</button>
                </div>
                <div class="shop-tabs">
                    <button class="shop-tab active" data-category="protocols">Protocols</button>
                    <button class="shop-tab" data-category="upgrades">Upgrades</button>
                    <button class="shop-tab" data-category="premium">Premium</button>
                </div>
                <div id="shop-items" class="shop-items">
                    <!-- Shop items will be dynamically generated -->
                </div>
            </div>
        </div>

        <!-- Notification System -->
        <div id="notifications" class="notifications"></div>

        <!-- Tutorial Overlay -->
        <div id="tutorial-overlay" class="tutorial-overlay hidden">
            <div class="tutorial-content">
                <h3 id="tutorial-title">Welcome to DeFi Dynasty!</h3>
                <p id="tutorial-text">Click on the grid to build your first Automated Market Maker (AMM) protocol.</p>
                <div class="tutorial-controls">
                    <button id="tutorial-skip">Skip Tutorial</button>
                    <button id="tutorial-next">Next</button>
                </div>
            </div>
        </div>
    </div>

        <!-- Achievement System -->
        <div id="achievement-popup" class="achievement-popup hidden">
            <div class="achievement-content">
                <div class="achievement-icon">🏆</div>
                <div class="achievement-info">
                    <h4 class="achievement-title">Achievement Unlocked!</h4>
                    <p class="achievement-description">First Protocol Builder</p>
                </div>
                <div class="achievement-glow"></div>
            </div>
        </div>

        <!-- Daily Challenge Panel -->
        <div id="daily-challenge" class="daily-challenge">
            <div class="challenge-header">
                <span class="challenge-icon">🎯</span>
                <span class="challenge-title">Daily Challenge</span>
                <span class="challenge-timer">23:45:12</span>
            </div>
            <div class="challenge-progress">
                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
                <span class="progress-text">Build 3 Protocols (1/3)</span>
            </div>
        </div>

        <!-- Enhanced Notification System -->
        <div id="notifications" class="notifications"></div>

        <!-- Floating Damage/Earnings Text Container -->
        <div id="floating-texts" class="floating-texts"></div>

        <!-- Audio Elements -->
        <audio id="background-music" loop>
            <source src="assets/audio/background.mp3" type="audio/mpeg">
        </audio>
    </div>

    <!-- Enhanced Scripts -->
    <script src="js/enhanced/audioManager.js"></script>
    <script src="js/enhanced/particleSystem.js"></script>
    <script src="js/enhanced/achievementSystem.js"></script>
    <script src="js/enhanced/phaserGame.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/gameState.js"></script>
    <script src="js/protocols.js"></script>
    <script src="js/shop.js"></script>
    <script src="js/tutorial.js"></script>
    <script src="js/enhanced/enhancedUI.js"></script>
    <script src="js/enhanced/enhancedGame.js"></script>

    <!-- Initialize Enhanced Features -->
    <script>
        // Initialize particle background
        particlesJS('particles-js', {
            particles: {
                number: { value: 80, density: { enable: true, value_area: 800 } },
                color: { value: '#00d4ff' },
                shape: { type: 'circle' },
                opacity: { value: 0.5, random: false },
                size: { value: 3, random: true },
                line_linked: {
                    enable: true,
                    distance: 150,
                    color: '#00d4ff',
                    opacity: 0.4,
                    width: 1
                },
                move: {
                    enable: true,
                    speed: 2,
                    direction: 'none',
                    random: false,
                    straight: false,
                    out_mode: 'out',
                    bounce: false
                }
            },
            interactivity: {
                detect_on: 'canvas',
                events: {
                    onhover: { enable: true, mode: 'repulse' },
                    onclick: { enable: true, mode: 'push' },
                    resize: true
                }
            },
            retina_detect: true
        });
    </script>
</body>
</html>
