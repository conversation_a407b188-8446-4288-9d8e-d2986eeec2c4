<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeFi Dynasty - Build Your Blockchain Empire</title>
    <link rel="stylesheet" href="styles/main.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
</head>
<body>
    <div id="game-container">
        <!-- Loading Screen -->
        <div id="loading-screen" class="screen active">
            <div class="loading-content">
                <h1 class="game-title">DeFi Dynasty</h1>
                <p class="game-subtitle">Build Your Blockchain Empire</p>
                <div class="loading-bar">
                    <div class="loading-progress"></div>
                </div>
                <p class="loading-text">Initializing blockchain...</p>
            </div>
        </div>

        <!-- Main Menu -->
        <div id="main-menu" class="screen">
            <div class="menu-content">
                <h1 class="game-title">DeFi Dynasty</h1>
                <div class="menu-buttons">
                    <button id="new-game-btn" class="menu-btn primary">New Empire</button>
                    <button id="continue-btn" class="menu-btn secondary">Continue</button>
                    <button id="tutorial-btn" class="menu-btn secondary">Tutorial</button>
                    <button id="leaderboard-btn" class="menu-btn secondary">Leaderboard</button>
                </div>
            </div>
        </div>

        <!-- Game HUD -->
        <div id="game-hud" class="hud hidden">
            <div class="hud-top">
                <div class="resources">
                    <div class="resource">
                        <span class="resource-icon">💰</span>
                        <span class="resource-label">ETH</span>
                        <span id="eth-balance" class="resource-value">1000</span>
                    </div>
                    <div class="resource">
                        <span class="resource-icon">🪙</span>
                        <span class="resource-label">Tokens</span>
                        <span id="token-balance" class="resource-value">0</span>
                    </div>
                    <div class="resource">
                        <span class="resource-icon">⚡</span>
                        <span class="resource-label">Energy</span>
                        <span id="energy-balance" class="resource-value">100</span>
                    </div>
                    <div class="resource">
                        <span class="resource-icon">🏆</span>
                        <span class="resource-label">Level</span>
                        <span id="player-level" class="resource-value">1</span>
                    </div>
                </div>
                <div class="hud-controls">
                    <button id="pause-btn" class="hud-btn">⏸️</button>
                    <button id="settings-btn" class="hud-btn">⚙️</button>
                    <button id="shop-btn" class="hud-btn">🛒</button>
                </div>
            </div>
            <div class="hud-bottom">
                <div class="protocol-tabs">
                    <button class="protocol-tab active" data-protocol="amm">AMM</button>
                    <button class="protocol-tab" data-protocol="lending">Lending</button>
                    <button class="protocol-tab" data-protocol="staking">Staking</button>
                    <button class="protocol-tab" data-protocol="governance">DAO</button>
                </div>
            </div>
        </div>

        <!-- Game World -->
        <div id="game-world" class="screen">
            <div class="world-container">
                <div id="protocol-grid" class="protocol-grid">
                    <!-- Protocol buildings will be dynamically generated here -->
                </div>
                <div class="world-info">
                    <h3 id="world-name">Ethereum Mainnet</h3>
                    <p id="world-description">The original blockchain where your DeFi empire begins</p>
                </div>
            </div>
        </div>

        <!-- Shop Modal -->
        <div id="shop-modal" class="modal hidden">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>DeFi Shop</h2>
                    <button class="close-btn">&times;</button>
                </div>
                <div class="shop-tabs">
                    <button class="shop-tab active" data-category="protocols">Protocols</button>
                    <button class="shop-tab" data-category="upgrades">Upgrades</button>
                    <button class="shop-tab" data-category="premium">Premium</button>
                </div>
                <div id="shop-items" class="shop-items">
                    <!-- Shop items will be dynamically generated -->
                </div>
            </div>
        </div>

        <!-- Notification System -->
        <div id="notifications" class="notifications"></div>

        <!-- Tutorial Overlay -->
        <div id="tutorial-overlay" class="tutorial-overlay hidden">
            <div class="tutorial-content">
                <h3 id="tutorial-title">Welcome to DeFi Dynasty!</h3>
                <p id="tutorial-text">Click on the grid to build your first Automated Market Maker (AMM) protocol.</p>
                <div class="tutorial-controls">
                    <button id="tutorial-skip">Skip Tutorial</button>
                    <button id="tutorial-next">Next</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/utils.js"></script>
    <script src="js/gameState.js"></script>
    <script src="js/protocols.js"></script>
    <script src="js/shop.js"></script>
    <script src="js/tutorial.js"></script>
    <script src="js/ui.js"></script>
    <script src="js/game.js"></script>
</body>
</html>
