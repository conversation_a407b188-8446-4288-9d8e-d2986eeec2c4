/**
 * UI Management System
 * Handles all user interface interactions and updates
 */

class UIManager {
    constructor() {
        this.activeModals = new Set();
        this.notifications = [];
        this.floatingTexts = [];
        
        this.setupGlobalEventListeners();
        this.initializeTooltips();
    }

    setupGlobalEventListeners() {
        // Global keyboard shortcuts
        document.addEventListener('keydown', (e) => this.handleKeyboardShortcuts(e));
        
        // Window resize handling
        window.addEventListener('resize', () => this.handleResize());
        
        // Visibility change (tab switching)
        document.addEventListener('visibilitychange', () => this.handleVisibilityChange());
        
        // Game state events
        document.addEventListener('game:resourceChanged', (e) => this.onResourceChanged(e.detail));
        document.addEventListener('game:levelUp', (e) => this.onLevelUp(e.detail));
        document.addEventListener('game:protocolBuilt', (e) => this.onProtocolBuilt(e.detail));
        document.addEventListener('game:protocolEarned', (e) => this.onProtocolEarned(e.detail));
        document.addEventListener('shop:boostActivated', (e) => this.onBoostActivated(e.detail));
        document.addEventListener('shop:boostExpired', (e) => this.onBoostExpired(e.detail));
    }

    handleKeyboardShortcuts(event) {
        // Don't handle shortcuts during tutorial or when typing
        if (tutorialManager.isTutorialActive() || this.isTyping(event.target)) {
            return;
        }

        switch (event.key.toLowerCase()) {
            case 'escape':
                this.closeTopModal();
                break;
            case 's':
                if (event.ctrlKey || event.metaKey) {
                    event.preventDefault();
                    gameState.save();
                    this.showNotification('Game saved!', 'success');
                }
                break;
            case 'p':
                if (window.game) {
                    window.game.pauseGame();
                }
                break;
            case 'h':
                this.toggleHUD();
                break;
            case '1':
            case '2':
            case '3':
            case '4':
                this.selectProtocolTab(parseInt(event.key) - 1);
                break;
        }
    }

    isTyping(element) {
        const typingElements = ['INPUT', 'TEXTAREA', 'SELECT'];
        return typingElements.includes(element.tagName) || element.contentEditable === 'true';
    }

    handleResize() {
        // Adjust UI elements for different screen sizes
        this.adjustForScreenSize();
        this.repositionFloatingTexts();
    }

    handleVisibilityChange() {
        if (document.hidden) {
            // Tab is hidden - save game state
            gameState.save();
        } else {
            // Tab is visible - resume any paused animations
            this.resumeAnimations();
        }
    }

    // Modal Management
    openModal(modalId) {
        const modal = document.getElementById(modalId);
        if (!modal) return false;

        modal.classList.remove('hidden');
        this.activeModals.add(modalId);
        
        // Add modal backdrop click handler
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.closeModal(modalId);
            }
        });

        AnimationUtils.fadeIn(modal, 200);
        return true;
    }

    closeModal(modalId) {
        const modal = document.getElementById(modalId);
        if (!modal) return false;

        AnimationUtils.fadeOut(modal, 200).then(() => {
            modal.classList.add('hidden');
            this.activeModals.delete(modalId);
        });

        return true;
    }

    closeTopModal() {
        if (this.activeModals.size > 0) {
            const topModal = Array.from(this.activeModals).pop();
            this.closeModal(topModal);
        }
    }

    // Notification System
    showNotification(message, type = 'info', duration = 3000) {
        const notification = this.createNotificationElement(message, type);
        const container = document.getElementById('notifications');
        
        container.appendChild(notification);
        this.notifications.push(notification);

        // Auto-remove after duration
        setTimeout(() => {
            this.removeNotification(notification);
        }, duration);

        // Animate in
        AnimationUtils.slideIn(notification, 'right', 300);
    }

    createNotificationElement(message, type) {
        const notification = DOMUtils.createElement('div', {
            className: `notification ${type}`
        }, message);

        // Add close button for longer notifications
        if (type === 'error') {
            const closeBtn = DOMUtils.createElement('button', {
                className: 'notification-close',
                style: 'margin-left: 10px; background: none; border: none; color: white; cursor: pointer;'
            }, '×');
            
            closeBtn.addEventListener('click', () => this.removeNotification(notification));
            notification.appendChild(closeBtn);
        }

        return notification;
    }

    removeNotification(notification) {
        if (notification.parentNode) {
            AnimationUtils.fadeOut(notification, 200).then(() => {
                notification.remove();
                const index = this.notifications.indexOf(notification);
                if (index > -1) {
                    this.notifications.splice(index, 1);
                }
            });
        }
    }

    // Floating Text System
    showFloatingText(text, x, y, className = 'floating-text', duration = 2000) {
        const floatingText = DOMUtils.createElement('div', {
            className: `floating-text ${className}`,
            style: `
                position: fixed;
                left: ${x}px;
                top: ${y}px;
                pointer-events: none;
                z-index: 10002;
                font-weight: bold;
                font-size: 1.2rem;
                color: #00d4ff;
                text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
            `
        }, text);

        document.body.appendChild(floatingText);
        this.floatingTexts.push(floatingText);

        // Animate upward and fade out
        this.animateFloatingText(floatingText, duration);

        // Remove after animation
        setTimeout(() => {
            this.removeFloatingText(floatingText);
        }, duration);
    }

    animateFloatingText(element, duration) {
        const startY = parseInt(element.style.top);
        const endY = startY - 100;
        const startTime = performance.now();

        function animate(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // Ease out animation
            const easeProgress = 1 - Math.pow(1 - progress, 3);
            
            const currentY = startY + (endY - startY) * easeProgress;
            const opacity = 1 - progress;
            
            element.style.top = `${currentY}px`;
            element.style.opacity = opacity;
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        }

        requestAnimationFrame(animate);
    }

    removeFloatingText(floatingText) {
        if (floatingText.parentNode) {
            floatingText.remove();
            const index = this.floatingTexts.indexOf(floatingText);
            if (index > -1) {
                this.floatingTexts.splice(index, 1);
            }
        }
    }

    repositionFloatingTexts() {
        // Reposition floating texts if window is resized
        this.floatingTexts.forEach(text => {
            const rect = text.getBoundingClientRect();
            if (rect.right > window.innerWidth) {
                text.style.left = `${window.innerWidth - rect.width - 20}px`;
            }
        });
    }

    // Tooltip System
    initializeTooltips() {
        this.tooltip = DOMUtils.createElement('div', {
            className: 'tooltip',
            style: `
                position: fixed;
                background: rgba(0, 0, 0, 0.9);
                color: white;
                padding: 8px 12px;
                border-radius: 6px;
                font-size: 0.9rem;
                pointer-events: none;
                z-index: 10003;
                opacity: 0;
                transition: opacity 0.2s ease;
                max-width: 250px;
                word-wrap: break-word;
            `
        });

        document.body.appendChild(this.tooltip);
        this.setupTooltipListeners();
    }

    setupTooltipListeners() {
        document.addEventListener('mouseover', (e) => {
            const element = e.target.closest('[data-tooltip]');
            if (element) {
                this.showTooltip(element.dataset.tooltip, e.pageX, e.pageY);
            }
        });

        document.addEventListener('mouseout', (e) => {
            const element = e.target.closest('[data-tooltip]');
            if (element) {
                this.hideTooltip();
            }
        });

        document.addEventListener('mousemove', (e) => {
            if (this.tooltip.style.opacity === '1') {
                this.updateTooltipPosition(e.pageX, e.pageY);
            }
        });
    }

    showTooltip(text, x, y) {
        this.tooltip.textContent = text;
        this.updateTooltipPosition(x, y);
        this.tooltip.style.opacity = '1';
    }

    hideTooltip() {
        this.tooltip.style.opacity = '0';
    }

    updateTooltipPosition(x, y) {
        const tooltipRect = this.tooltip.getBoundingClientRect();
        const windowWidth = window.innerWidth;
        const windowHeight = window.innerHeight;

        let left = x + 10;
        let top = y - tooltipRect.height - 10;

        // Adjust if tooltip goes off screen
        if (left + tooltipRect.width > windowWidth) {
            left = x - tooltipRect.width - 10;
        }
        if (top < 0) {
            top = y + 10;
        }

        this.tooltip.style.left = `${left}px`;
        this.tooltip.style.top = `${top}px`;
    }

    // HUD Management
    toggleHUD() {
        const hud = document.getElementById('game-hud');
        hud.classList.toggle('hidden');
    }

    updateResourceDisplay() {
        const resources = gameState.data.resources;
        
        document.getElementById('eth-balance').textContent = StringUtils.formatNumber(resources.eth);
        document.getElementById('token-balance').textContent = StringUtils.formatNumber(resources.tokens);
        document.getElementById('energy-balance').textContent = `${resources.energy}/${resources.maxEnergy}`;
        document.getElementById('player-level').textContent = gameState.data.player.level;
    }

    // Protocol Tab Management
    selectProtocolTab(index) {
        const tabs = document.querySelectorAll('.protocol-tab');
        if (tabs[index] && !tabs[index].disabled) {
            tabs[index].click();
        }
    }

    // Screen Size Adjustments
    adjustForScreenSize() {
        const isMobile = window.innerWidth <= 768;
        
        if (isMobile) {
            document.body.classList.add('mobile');
            this.adjustMobileUI();
        } else {
            document.body.classList.remove('mobile');
            this.adjustDesktopUI();
        }
    }

    adjustMobileUI() {
        // Mobile-specific UI adjustments
        const protocolGrid = document.getElementById('protocol-grid');
        if (protocolGrid) {
            protocolGrid.style.gridTemplateColumns = 'repeat(6, 50px)';
            protocolGrid.style.gridTemplateRows = 'repeat(8, 50px)';
        }
    }

    adjustDesktopUI() {
        // Desktop-specific UI adjustments
        const protocolGrid = document.getElementById('protocol-grid');
        if (protocolGrid) {
            protocolGrid.style.gridTemplateColumns = 'repeat(8, 80px)';
            protocolGrid.style.gridTemplateRows = 'repeat(6, 80px)';
        }
    }

    // Animation Management
    resumeAnimations() {
        // Resume any paused CSS animations
        document.querySelectorAll('[style*="animation-play-state: paused"]').forEach(element => {
            element.style.animationPlayState = 'running';
        });
    }

    // Event Handlers
    onResourceChanged(data) {
        this.updateResourceDisplay();
        
        if (data.amount > 0) {
            // Show floating text for resource gains
            const resourceElement = document.querySelector(`#${data.type}-balance`);
            if (resourceElement) {
                const rect = resourceElement.getBoundingClientRect();
                this.showFloatingText(
                    `+${StringUtils.formatNumber(data.amount)}`,
                    rect.left + rect.width / 2,
                    rect.top,
                    'resource-gain'
                );
            }
        }
    }

    onLevelUp(data) {
        this.updateResourceDisplay();
        this.showNotification(`Level Up! You are now level ${data.level}`, 'success', 4000);
        
        // Show dramatic level up effect
        const levelElement = document.getElementById('player-level');
        if (levelElement) {
            AnimationUtils.bounce(levelElement, 15, 800);
            
            const rect = levelElement.getBoundingClientRect();
            this.showFloatingText(
                `LEVEL ${data.level}!`,
                rect.left + rect.width / 2,
                rect.top,
                'level-up',
                3000
            );
        }
    }

    onProtocolBuilt(protocol) {
        this.showNotification(`${protocolManager.protocolTypes[protocol.type].name} built!`, 'success');
    }

    onProtocolEarned(data) {
        // Visual feedback for protocol earnings
        const protocolElement = document.querySelector(`[data-protocol-id="${data.protocolId}"]`);
        if (protocolElement) {
            const rect = protocolElement.getBoundingClientRect();
            this.showFloatingText(
                `+${StringUtils.formatNumber(data.earning)}`,
                rect.left + rect.width / 2,
                rect.top,
                'protocol-earning'
            );
        }
    }

    onBoostActivated(boost) {
        this.showNotification(`${StringUtils.camelToTitle(boost.type)} boost activated!`, 'success');
    }

    onBoostExpired(data) {
        this.showNotification(`${StringUtils.camelToTitle(data.type)} boost expired`, 'info');
    }
}

// Initialize UI manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.uiManager = new UIManager();
});
