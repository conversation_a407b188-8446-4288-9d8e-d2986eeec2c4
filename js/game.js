/**
 * Main Game Controller
 * Orchestrates all game systems and manages the game loop
 */

class Game {
    constructor() {
        this.currentScreen = 'loading';
        this.isInitialized = false;
        this.selectedProtocolType = 'amm';
        this.gridSize = { width: 8, height: 6 };
        this.occupiedCells = new Set();
        
        this.init();
    }

    async init() {
        console.log('Initializing DeFi Dynasty...');
        
        // Show loading screen
        this.showScreen('loading');
        
        // Simulate loading time
        await this.simulateLoading();
        
        // Initialize game systems
        this.setupEventListeners();
        this.setupUI();
        
        // Check for existing save data
        if (gameState.hasSaveData()) {
            this.showScreen('main-menu');
            document.getElementById('continue-btn').style.display = 'block';
        } else {
            this.showScreen('main-menu');
            document.getElementById('continue-btn').style.display = 'none';
        }
        
        this.isInitialized = true;
        console.log('Game initialized successfully!');
    }

    async simulateLoading() {
        const loadingProgress = document.querySelector('.loading-progress');
        const loadingText = document.querySelector('.loading-text');
        
        const steps = [
            { progress: 20, text: 'Connecting to blockchain...' },
            { progress: 40, text: 'Loading smart contracts...' },
            { progress: 60, text: 'Initializing DeFi protocols...' },
            { progress: 80, text: 'Setting up liquidity pools...' },
            { progress: 100, text: 'Ready to build your empire!' }
        ];

        for (const step of steps) {
            loadingProgress.style.width = `${step.progress}%`;
            loadingText.textContent = step.text;
            await this.sleep(800);
        }
    }

    setupEventListeners() {
        // Menu buttons
        document.getElementById('new-game-btn').addEventListener('click', () => this.startNewGame());
        document.getElementById('continue-btn').addEventListener('click', () => this.continueGame());
        document.getElementById('tutorial-btn').addEventListener('click', () => this.startTutorial());
        
        // HUD buttons
        document.getElementById('pause-btn').addEventListener('click', () => this.pauseGame());
        document.getElementById('settings-btn').addEventListener('click', () => this.showSettings());
        document.getElementById('shop-btn').addEventListener('click', () => this.showShop());
        
        // Protocol tabs
        document.querySelectorAll('.protocol-tab').forEach(tab => {
            tab.addEventListener('click', (e) => this.selectProtocolType(e.target.dataset.protocol));
        });

        // Game state events
        document.addEventListener('game:resourceChanged', (e) => this.onResourceChanged(e.detail));
        document.addEventListener('game:levelUp', (e) => this.onLevelUp(e.detail));
        document.addEventListener('game:protocolBuilt', (e) => this.onProtocolBuilt(e.detail));
        document.addEventListener('game:protocolEarned', (e) => this.onProtocolEarned(e.detail));
        
        // Window events
        window.addEventListener('beforeunload', () => gameState.save());
        window.addEventListener('blur', () => gameState.save());
    }

    setupUI() {
        this.updateResourceDisplay();
        this.updatePlayerLevel();
        this.createProtocolGrid();
    }

    // Screen Management
    showScreen(screenId) {
        // Hide all screens
        document.querySelectorAll('.screen').forEach(screen => {
            screen.classList.remove('active');
        });
        
        // Show target screen
        const targetScreen = document.getElementById(screenId);
        if (targetScreen) {
            targetScreen.classList.add('active');
            this.currentScreen = screenId;
        }

        // Show/hide HUD based on screen
        const hud = document.getElementById('game-hud');
        if (screenId === 'game-world') {
            hud.classList.remove('hidden');
        } else {
            hud.classList.add('hidden');
        }
    }

    // Game Flow
    startNewGame() {
        gameState.reset();
        this.startGame();
    }

    continueGame() {
        this.startGame();
        this.loadGameState();
    }

    startGame() {
        this.showScreen('game-world');
        this.updateUI();
        
        // Start tutorial if not completed
        if (!gameState.data.tutorial.completed && !gameState.data.tutorial.skipped) {
            this.startTutorial();
        }
    }

    startTutorial() {
        // Tutorial will be implemented in tutorial.js
        console.log('Starting tutorial...');
    }

    loadGameState() {
        // Restore built protocols to the grid
        const protocols = gameState.getProtocols();
        protocols.forEach(protocol => {
            this.addProtocolToGrid(protocol);
            protocolManager.activateProtocol(protocol);
        });
    }

    // Protocol Grid Management
    createProtocolGrid() {
        const grid = document.getElementById('protocol-grid');
        grid.innerHTML = '';
        
        for (let row = 0; row < this.gridSize.height; row++) {
            for (let col = 0; col < this.gridSize.width; col++) {
                const cell = document.createElement('div');
                cell.className = 'grid-cell';
                cell.dataset.row = row;
                cell.dataset.col = col;
                cell.dataset.position = `${row}-${col}`;
                
                cell.addEventListener('click', (e) => this.onGridCellClick(e));
                
                grid.appendChild(cell);
            }
        }
    }

    onGridCellClick(event) {
        const cell = event.currentTarget;
        const position = cell.dataset.position;
        
        if (cell.classList.contains('occupied')) {
            // Show protocol details/upgrade options
            this.showProtocolDetails(position);
            return;
        }

        // Try to build selected protocol
        this.buildProtocol(this.selectedProtocolType, position);
    }

    buildProtocol(type, position) {
        const result = protocolManager.buildProtocol(type, position);
        
        if (result.success) {
            this.addProtocolToGrid(result.protocol);
            this.showNotification(`${protocolManager.protocolTypes[type].name} built successfully!`, 'success');
            this.updateUI();
        } else {
            this.showNotification(result.reason, 'error');
        }
    }

    addProtocolToGrid(protocol) {
        const cell = document.querySelector(`[data-position="${protocol.position}"]`);
        if (!cell) return;

        const protocolDef = protocolManager.protocolTypes[protocol.type];
        
        cell.classList.add('occupied');
        cell.innerHTML = `
            <div class="protocol-building ${protocol.type}" data-protocol-id="${protocol.id}">
                <div class="protocol-icon">${protocolDef.icon}</div>
                <div class="protocol-level">L${protocol.level}</div>
            </div>
        `;

        this.occupiedCells.add(protocol.position);
    }

    selectProtocolType(type) {
        // Update selected protocol type
        this.selectedProtocolType = type;
        
        // Update tab appearance
        document.querySelectorAll('.protocol-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-protocol="${type}"]`).classList.add('active');
        
        // Update grid cell hover effects or preview
        this.updateGridPreview(type);
    }

    updateGridPreview(type) {
        const protocolDef = protocolManager.protocolTypes[type];
        const canBuild = protocolManager.canBuildProtocol(type, gameState.getPlayerLevel());
        
        document.querySelectorAll('.grid-cell:not(.occupied)').forEach(cell => {
            if (canBuild.canBuild) {
                cell.style.borderColor = '#00d4ff';
                cell.title = `Build ${protocolDef.name} - Cost: ${this.formatCosts(protocolDef.baseCost)}`;
            } else {
                cell.style.borderColor = '#ff6b6b';
                cell.title = canBuild.reason;
            }
        });
    }

    // UI Updates
    updateUI() {
        this.updateResourceDisplay();
        this.updatePlayerLevel();
        this.updateProtocolTabs();
    }

    updateResourceDisplay() {
        document.getElementById('eth-balance').textContent = this.formatNumber(gameState.getResource('eth'));
        document.getElementById('token-balance').textContent = this.formatNumber(gameState.getResource('tokens'));
        document.getElementById('energy-balance').textContent = `${gameState.getResource('energy')}/${gameState.getResource('maxEnergy')}`;
    }

    updatePlayerLevel() {
        document.getElementById('player-level').textContent = gameState.getPlayerLevel();
    }

    updateProtocolTabs() {
        const playerLevel = gameState.getPlayerLevel();
        const availableProtocols = protocolManager.getAvailableProtocols(playerLevel);
        
        document.querySelectorAll('.protocol-tab').forEach(tab => {
            const protocolType = tab.dataset.protocol;
            if (availableProtocols[protocolType]) {
                tab.style.display = 'block';
                tab.disabled = false;
            } else {
                tab.style.display = 'none';
            }
        });
    }

    // Event Handlers
    onResourceChanged(data) {
        this.updateResourceDisplay();
        
        if (data.amount > 0) {
            this.showFloatingText(`+${this.formatNumber(data.amount)} ${data.type.toUpperCase()}`, 'resource-gain');
        }
    }

    onLevelUp(data) {
        this.updatePlayerLevel();
        this.updateProtocolTabs();
        this.showNotification(`Level Up! You are now level ${data.level}`, 'success');
        this.showFloatingText(`LEVEL ${data.level}!`, 'level-up');
    }

    onProtocolBuilt(protocol) {
        console.log('Protocol built:', protocol);
    }

    onProtocolEarned(data) {
        // Visual feedback for earnings will be added here
    }

    // Utility Methods
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        
        document.getElementById('notifications').appendChild(notification);
        
        // Auto-remove after 3 seconds
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    showFloatingText(text, className) {
        // Floating text animation will be implemented here
        console.log(`Floating text: ${text}`);
    }

    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return Math.floor(num).toString();
    }

    formatCosts(costs) {
        return Object.entries(costs)
            .map(([resource, amount]) => `${amount} ${resource.toUpperCase()}`)
            .join(', ');
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Game State Management
    pauseGame() {
        // Pause functionality will be implemented here
        console.log('Game paused');
    }

    showSettings() {
        // Settings modal will be implemented here
        console.log('Show settings');
    }

    showShop() {
        // Shop modal will be implemented here
        console.log('Show shop');
    }

    showProtocolDetails(position) {
        // Protocol details modal will be implemented here
        console.log('Show protocol details for position:', position);
    }
}

// Initialize game when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.game = new Game();
});
