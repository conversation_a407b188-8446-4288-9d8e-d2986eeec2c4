/**
 * Shop System
 * Manages in-game purchases, upgrades, and premium items
 */

class ShopManager {
    constructor() {
        this.isOpen = false;
        this.currentCategory = 'protocols';
        
        this.shopItems = {
            protocols: {
                'amm-blueprint': {
                    id: 'amm-blueprint',
                    name: 'AMM Blueprint',
                    description: 'Instantly build an AMM protocol anywhere on the grid',
                    icon: '📋',
                    cost: { eth: 200, tokens: 50 },
                    type: 'consumable',
                    category: 'protocols',
                    unlockLevel: 2
                },
                'protocol-booster': {
                    id: 'protocol-booster',
                    name: 'Protocol Booster',
                    description: 'Double earnings for all protocols for 5 minutes',
                    icon: '🚀',
                    cost: { tokens: 100 },
                    type: 'consumable',
                    category: 'protocols',
                    unlockLevel: 3,
                    duration: 300000 // 5 minutes
                }
            },
            upgrades: {
                'energy-capacity': {
                    id: 'energy-capacity',
                    name: 'Energy Capacity Upgrade',
                    description: 'Permanently increase maximum energy by 25',
                    icon: '🔋',
                    cost: { eth: 500, tokens: 200 },
                    type: 'permanent',
                    category: 'upgrades',
                    unlockLevel: 4,
                    effect: { maxEnergy: 25 }
                },
                'auto-collector': {
                    id: 'auto-collector',
                    name: 'Auto Collector',
                    description: 'Automatically collect earnings every 30 seconds',
                    icon: '🤖',
                    cost: { eth: 1000, tokens: 500 },
                    type: 'permanent',
                    category: 'upgrades',
                    unlockLevel: 5
                },
                'efficiency-multiplier': {
                    id: 'efficiency-multiplier',
                    name: 'Global Efficiency Multiplier',
                    description: 'Increase all protocol efficiency by 10%',
                    icon: '⚡',
                    cost: { eth: 750, tokens: 300 },
                    type: 'permanent',
                    category: 'upgrades',
                    unlockLevel: 6,
                    stackable: true,
                    maxStacks: 5,
                    effect: { globalEfficiency: 0.1 }
                }
            },
            premium: {
                'golden-protocol': {
                    id: 'golden-protocol',
                    name: 'Golden Protocol Package',
                    description: 'Unlock exclusive golden protocols with 3x earnings',
                    icon: '👑',
                    cost: { premium: 9.99 },
                    type: 'premium',
                    category: 'premium',
                    unlockLevel: 1
                },
                'vip-pass': {
                    id: 'vip-pass',
                    name: 'VIP Pass (30 days)',
                    description: 'Double XP gain, exclusive rewards, and priority support',
                    icon: '💎',
                    cost: { premium: 19.99 },
                    type: 'premium',
                    category: 'premium',
                    unlockLevel: 1,
                    duration: 2592000000 // 30 days
                },
                'mega-starter-pack': {
                    id: 'mega-starter-pack',
                    name: 'Mega Starter Pack',
                    description: '10,000 ETH, 5,000 Tokens, and exclusive blueprints',
                    icon: '🎁',
                    cost: { premium: 4.99 },
                    type: 'premium',
                    category: 'premium',
                    unlockLevel: 1,
                    rewards: { eth: 10000, tokens: 5000 }
                }
            }
        };

        this.activeBoosts = new Map();
        this.setupEventListeners();
        this.startBoostTimer();
    }

    setupEventListeners() {
        // Shop modal controls
        document.getElementById('shop-btn').addEventListener('click', () => this.openShop());
        document.querySelector('#shop-modal .close-btn').addEventListener('click', () => this.closeShop());
        
        // Shop tabs
        document.querySelectorAll('.shop-tab').forEach(tab => {
            tab.addEventListener('click', (e) => this.switchCategory(e.target.dataset.category));
        });

        // Close modal when clicking outside
        document.getElementById('shop-modal').addEventListener('click', (e) => {
            if (e.target.id === 'shop-modal') {
                this.closeShop();
            }
        });

        // Listen for game events
        document.addEventListener('game:levelUp', () => this.updateShopItems());
    }

    openShop() {
        this.isOpen = true;
        document.getElementById('shop-modal').classList.remove('hidden');
        this.updateShopItems();
        AnimationUtils.fadeIn(document.getElementById('shop-modal'), 200);
    }

    closeShop() {
        this.isOpen = false;
        AnimationUtils.fadeOut(document.getElementById('shop-modal'), 200).then(() => {
            document.getElementById('shop-modal').classList.add('hidden');
        });
    }

    switchCategory(category) {
        this.currentCategory = category;
        
        // Update tab appearance
        document.querySelectorAll('.shop-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-category="${category}"]`).classList.add('active');
        
        this.updateShopItems();
    }

    updateShopItems() {
        const container = document.getElementById('shop-items');
        container.innerHTML = '';
        
        const items = this.shopItems[this.currentCategory];
        const playerLevel = gameState.getPlayerLevel();
        
        Object.values(items).forEach(item => {
            if (playerLevel >= item.unlockLevel) {
                const itemElement = this.createShopItemElement(item);
                container.appendChild(itemElement);
            }
        });
    }

    createShopItemElement(item) {
        const canAfford = this.canAffordItem(item);
        const isPurchased = this.isItemPurchased(item.id);
        const isStackable = item.stackable && !isPurchased;
        
        const element = DOMUtils.createElement('div', {
            className: `shop-item ${canAfford ? 'affordable' : 'expensive'} ${isPurchased && !isStackable ? 'purchased' : ''}`,
            dataset: { itemId: item.id }
        });

        const costDisplay = this.formatItemCost(item.cost);
        const stackInfo = item.stackable ? this.getStackInfo(item.id) : null;
        
        element.innerHTML = `
            <div class="item-icon">${item.icon}</div>
            <div class="item-info">
                <h4 class="item-name">${item.name}</h4>
                <p class="item-description">${item.description}</p>
                ${stackInfo ? `<p class="stack-info">Owned: ${stackInfo.current}/${stackInfo.max}</p>` : ''}
                <div class="item-cost">${costDisplay}</div>
            </div>
            <button class="purchase-btn ${canAfford ? '' : 'disabled'}" 
                    ${canAfford ? '' : 'disabled'}>
                ${isPurchased && !isStackable ? 'Owned' : 'Buy'}
            </button>
        `;

        // Add purchase event listener
        const purchaseBtn = element.querySelector('.purchase-btn');
        purchaseBtn.addEventListener('click', () => this.purchaseItem(item.id));

        return element;
    }

    canAffordItem(item) {
        if (item.cost.premium) {
            return true; // Premium items are always "affordable" for demo
        }
        
        return gameState.canAfford(item.cost);
    }

    isItemPurchased(itemId) {
        return gameState.data.shop.purchases.includes(itemId);
    }

    getStackInfo(itemId) {
        const item = this.findItemById(itemId);
        if (!item || !item.stackable) return null;
        
        const purchased = gameState.data.shop.purchases.filter(id => id === itemId).length;
        return {
            current: purchased,
            max: item.maxStacks
        };
    }

    purchaseItem(itemId) {
        const item = this.findItemById(itemId);
        if (!item) return;

        // Check if already purchased (for non-stackable items)
        if (this.isItemPurchased(itemId) && !item.stackable) {
            this.showPurchaseMessage('Item already owned!', 'error');
            return;
        }

        // Check stack limit
        if (item.stackable) {
            const stackInfo = this.getStackInfo(itemId);
            if (stackInfo.current >= stackInfo.max) {
                this.showPurchaseMessage('Maximum stacks reached!', 'error');
                return;
            }
        }

        // Handle premium purchases
        if (item.cost.premium) {
            this.handlePremiumPurchase(item);
            return;
        }

        // Check if can afford
        if (!this.canAffordItem(item)) {
            this.showPurchaseMessage('Insufficient resources!', 'error');
            return;
        }

        // Process purchase
        if (gameState.spendResources(item.cost)) {
            this.applyItemEffect(item);
            gameState.data.shop.purchases.push(itemId);
            
            this.showPurchaseMessage(`${item.name} purchased!`, 'success');
            this.updateShopItems();
            
            // Update main UI
            if (window.game) {
                window.game.updateUI();
            }
        }
    }

    handlePremiumPurchase(item) {
        // Simulate premium purchase flow
        const confirmPurchase = confirm(
            `This will simulate a premium purchase of $${item.cost.premium}.\n\n` +
            `${item.name}\n${item.description}\n\n` +
            `In a real game, this would integrate with payment processors like Stripe or crypto wallets.`
        );

        if (confirmPurchase) {
            // Simulate successful payment
            setTimeout(() => {
                this.applyItemEffect(item);
                gameState.data.shop.premiumItems.push(item.id);
                
                this.showPurchaseMessage(`${item.name} purchased! (Simulated)`, 'success');
                this.updateShopItems();
                
                if (window.game) {
                    window.game.updateUI();
                }
            }, 1000);
        }
    }

    applyItemEffect(item) {
        switch (item.type) {
            case 'consumable':
                this.applyConsumableEffect(item);
                break;
            case 'permanent':
                this.applyPermanentEffect(item);
                break;
            case 'premium':
                this.applyPremiumEffect(item);
                break;
        }
    }

    applyConsumableEffect(item) {
        switch (item.id) {
            case 'amm-blueprint':
                // Add blueprint to inventory (would be implemented in inventory system)
                break;
            case 'protocol-booster':
                this.activateBoost('earnings', 2.0, item.duration);
                break;
        }
    }

    applyPermanentEffect(item) {
        if (item.effect) {
            Object.entries(item.effect).forEach(([key, value]) => {
                switch (key) {
                    case 'maxEnergy':
                        gameState.data.resources.maxEnergy += value;
                        break;
                    case 'globalEfficiency':
                        // This would be applied in the protocol earning calculations
                        break;
                }
            });
        }

        // Special permanent effects
        switch (item.id) {
            case 'auto-collector':
                this.enableAutoCollector();
                break;
        }
    }

    applyPremiumEffect(item) {
        if (item.rewards) {
            Object.entries(item.rewards).forEach(([resource, amount]) => {
                gameState.addResource(resource, amount);
            });
        }

        switch (item.id) {
            case 'vip-pass':
                this.activateVIPPass(item.duration);
                break;
            case 'golden-protocol':
                // Unlock golden protocols
                break;
        }
    }

    activateBoost(type, multiplier, duration) {
        const boost = {
            type,
            multiplier,
            endTime: Date.now() + duration,
            startTime: Date.now()
        };
        
        this.activeBoosts.set(type, boost);
        
        // Notify UI
        EventUtils.dispatch('shop:boostActivated', boost);
    }

    enableAutoCollector() {
        // Auto-collector would be implemented here
        console.log('Auto-collector enabled!');
    }

    activateVIPPass(duration) {
        const vipData = {
            active: true,
            endTime: Date.now() + duration,
            startTime: Date.now()
        };
        
        gameState.data.shop.vipPass = vipData;
        EventUtils.dispatch('shop:vipActivated', vipData);
    }

    startBoostTimer() {
        setInterval(() => {
            const now = Date.now();
            
            for (const [type, boost] of this.activeBoosts) {
                if (now >= boost.endTime) {
                    this.activeBoosts.delete(type);
                    EventUtils.dispatch('shop:boostExpired', { type });
                }
            }
        }, 1000);
    }

    getActiveBoost(type) {
        return this.activeBoosts.get(type);
    }

    hasActiveBoost(type) {
        return this.activeBoosts.has(type);
    }

    // Utility methods
    findItemById(itemId) {
        for (const category of Object.values(this.shopItems)) {
            if (category[itemId]) {
                return category[itemId];
            }
        }
        return null;
    }

    formatItemCost(cost) {
        if (cost.premium) {
            return `$${cost.premium}`;
        }
        
        return Object.entries(cost)
            .map(([resource, amount]) => `${StringUtils.formatNumber(amount)} ${resource.toUpperCase()}`)
            .join(' + ');
    }

    showPurchaseMessage(message, type) {
        if (window.game) {
            window.game.showNotification(message, type);
        }
    }

    // Get global multipliers for other systems
    getGlobalMultipliers() {
        const multipliers = {
            earnings: 1,
            experience: 1,
            efficiency: 1
        };

        // Apply active boosts
        for (const boost of this.activeBoosts.values()) {
            if (boost.type === 'earnings') {
                multipliers.earnings *= boost.multiplier;
            }
        }

        // Apply VIP multipliers
        if (gameState.data.shop.vipPass && gameState.data.shop.vipPass.active) {
            const now = Date.now();
            if (now < gameState.data.shop.vipPass.endTime) {
                multipliers.experience *= 2;
            }
        }

        // Apply permanent upgrades
        const efficiencyStacks = gameState.data.shop.purchases.filter(id => id === 'efficiency-multiplier').length;
        multipliers.efficiency *= (1 + efficiencyStacks * 0.1);

        return multipliers;
    }
}

// Global shop manager instance
window.shopManager = new ShopManager();
