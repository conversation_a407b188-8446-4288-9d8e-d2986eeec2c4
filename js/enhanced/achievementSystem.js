/**
 * Enhanced Achievement System
 * Manages achievements, daily challenges, and player progression rewards
 */

class AchievementSystem {
    constructor() {
        this.achievements = new Map();
        this.dailyChallenges = new Map();
        this.unlockedAchievements = new Set();
        this.currentChallenge = null;
        this.challengeStartTime = null;
        
        this.initializeAchievements();
        this.initializeDailyChallenges();
        this.loadProgress();
        this.setupEventListeners();
    }

    initializeAchievements() {
        const achievementData = [
            // Building Achievements
            {
                id: 'first_protocol',
                name: 'First Steps',
                description: 'Build your first protocol',
                icon: '🏗️',
                category: 'building',
                requirement: { type: 'protocols_built', value: 1 },
                reward: { eth: 100, experience: 50 },
                rarity: 'common'
            },
            {
                id: 'protocol_master',
                name: 'Protocol Master',
                description: 'Build 10 protocols',
                icon: '🏭',
                category: 'building',
                requirement: { type: 'protocols_built', value: 10 },
                reward: { eth: 1000, tokens: 500, experience: 200 },
                rarity: 'rare'
            },
            {
                id: 'defi_empire',
                name: 'DeFi Empire',
                description: 'Build 50 protocols',
                icon: '🏰',
                category: 'building',
                requirement: { type: 'protocols_built', value: 50 },
                reward: { eth: 5000, tokens: 2000, experience: 1000 },
                rarity: 'legendary'
            },

            // Resource Achievements
            {
                id: 'first_thousand',
                name: 'Crypto Millionaire',
                description: 'Earn 1,000 ETH',
                icon: '💰',
                category: 'resources',
                requirement: { type: 'total_eth_earned', value: 1000 },
                reward: { tokens: 200, experience: 100 },
                rarity: 'common'
            },
            {
                id: 'whale_status',
                name: 'Crypto Whale',
                description: 'Earn 100,000 ETH',
                icon: '🐋',
                category: 'resources',
                requirement: { type: 'total_eth_earned', value: 100000 },
                reward: { eth: 10000, tokens: 5000, experience: 500 },
                rarity: 'epic'
            },

            // Level Achievements
            {
                id: 'level_10',
                name: 'Rising Star',
                description: 'Reach level 10',
                icon: '⭐',
                category: 'progression',
                requirement: { type: 'player_level', value: 10 },
                reward: { eth: 2000, tokens: 1000, experience: 300 },
                rarity: 'uncommon'
            },
            {
                id: 'level_25',
                name: 'DeFi Expert',
                description: 'Reach level 25',
                icon: '🎓',
                category: 'progression',
                requirement: { type: 'player_level', value: 25 },
                reward: { eth: 5000, tokens: 2500, experience: 750 },
                rarity: 'rare'
            },

            // Speed Achievements
            {
                id: 'speed_builder',
                name: 'Speed Builder',
                description: 'Build 5 protocols in 60 seconds',
                icon: '⚡',
                category: 'speed',
                requirement: { type: 'protocols_per_minute', value: 5 },
                reward: { eth: 1500, experience: 200 },
                rarity: 'rare'
            },

            // Special Achievements
            {
                id: 'tutorial_complete',
                name: 'Quick Learner',
                description: 'Complete the tutorial',
                icon: '📚',
                category: 'special',
                requirement: { type: 'tutorial_completed', value: true },
                reward: { eth: 500, tokens: 100, experience: 100 },
                rarity: 'common'
            },
            {
                id: 'no_energy_waste',
                name: 'Energy Efficient',
                description: 'Never let energy reach maximum for 10 minutes',
                icon: '🔋',
                category: 'efficiency',
                requirement: { type: 'energy_efficiency_time', value: 600 },
                reward: { tokens: 300, experience: 150 },
                rarity: 'uncommon'
            },

            // Collection Achievements
            {
                id: 'protocol_collector',
                name: 'Protocol Collector',
                description: 'Build at least one of each protocol type',
                icon: '🎯',
                category: 'collection',
                requirement: { type: 'unique_protocols', value: 4 },
                reward: { eth: 3000, tokens: 1500, experience: 400 },
                rarity: 'epic'
            }
        ];

        achievementData.forEach(achievement => {
            this.achievements.set(achievement.id, achievement);
        });
    }

    initializeDailyChallenges() {
        const challengeTemplates = [
            {
                id: 'daily_builder',
                name: 'Daily Builder',
                description: 'Build {target} protocols today',
                icon: '🏗️',
                type: 'protocols_built',
                baseTarget: 3,
                reward: { eth: 500, tokens: 200, experience: 100 }
            },
            {
                id: 'daily_earner',
                name: 'Daily Earner',
                description: 'Earn {target} ETH today',
                icon: '💰',
                type: 'eth_earned',
                baseTarget: 1000,
                reward: { tokens: 300, experience: 150 }
            },
            {
                id: 'daily_upgrader',
                name: 'Daily Upgrader',
                description: 'Upgrade protocols {target} times today',
                icon: '⬆️',
                type: 'upgrades_purchased',
                baseTarget: 2,
                reward: { eth: 800, tokens: 150, experience: 120 }
            },
            {
                id: 'daily_efficient',
                name: 'Daily Efficient',
                description: 'Keep energy above 50% for {target} minutes',
                icon: '⚡',
                type: 'energy_management',
                baseTarget: 30,
                reward: { eth: 600, tokens: 250, experience: 100 }
            }
        ];

        challengeTemplates.forEach(template => {
            this.dailyChallenges.set(template.id, template);
        });
    }

    setupEventListeners() {
        // Listen for game events to track progress
        document.addEventListener('game:protocolBuilt', (e) => this.onProtocolBuilt(e.detail));
        document.addEventListener('game:resourceChanged', (e) => this.onResourceChanged(e.detail));
        document.addEventListener('game:levelUp', (e) => this.onLevelUp(e.detail));
        document.addEventListener('game:protocolUpgraded', (e) => this.onProtocolUpgraded(e.detail));
        document.addEventListener('game:tutorialCompleted', () => this.onTutorialCompleted());
    }

    // Event Handlers
    onProtocolBuilt(protocol) {
        this.updateProgress('protocols_built', 1);
        this.updateDailyChallengeProgress('protocols_built', 1);
        this.checkSpeedBuilding();
    }

    onResourceChanged(data) {
        if (data.type === 'eth' && data.amount > 0) {
            this.updateProgress('total_eth_earned', data.amount);
            this.updateDailyChallengeProgress('eth_earned', data.amount);
        }
    }

    onLevelUp(data) {
        this.updateProgress('player_level', data.level);
        this.checkAchievement('level_10');
        this.checkAchievement('level_25');
    }

    onProtocolUpgraded(data) {
        this.updateProgress('upgrades_purchased', 1);
        this.updateDailyChallengeProgress('upgrades_purchased', 1);
    }

    onTutorialCompleted() {
        this.updateProgress('tutorial_completed', true);
        this.checkAchievement('tutorial_complete');
    }

    // Progress Tracking
    updateProgress(type, value) {
        if (!gameState) return;

        const stats = gameState.data.statistics;
        
        switch (type) {
            case 'protocols_built':
                stats.protocolsBuilt += value;
                break;
            case 'total_eth_earned':
                stats.totalEthEarned += value;
                break;
            case 'player_level':
                // Level is already updated in gameState
                break;
            case 'upgrades_purchased':
                stats.upgradesPurchased = (stats.upgradesPurchased || 0) + value;
                break;
            case 'tutorial_completed':
                stats.tutorialCompleted = value;
                break;
        }

        this.checkAllAchievements();
    }

    checkAllAchievements() {
        for (const [id, achievement] of this.achievements) {
            if (!this.unlockedAchievements.has(id)) {
                this.checkAchievement(id);
            }
        }
    }

    checkAchievement(achievementId) {
        const achievement = this.achievements.get(achievementId);
        if (!achievement || this.unlockedAchievements.has(achievementId)) {
            return false;
        }

        const isUnlocked = this.isAchievementUnlocked(achievement);
        if (isUnlocked) {
            this.unlockAchievement(achievementId);
            return true;
        }
        return false;
    }

    isAchievementUnlocked(achievement) {
        if (!gameState) return false;

        const stats = gameState.data.statistics;
        const req = achievement.requirement;

        switch (req.type) {
            case 'protocols_built':
                return stats.protocolsBuilt >= req.value;
            case 'total_eth_earned':
                return stats.totalEthEarned >= req.value;
            case 'player_level':
                return gameState.data.player.level >= req.value;
            case 'tutorial_completed':
                return gameState.data.tutorial.completed === req.value;
            case 'upgrades_purchased':
                return (stats.upgradesPurchased || 0) >= req.value;
            case 'unique_protocols':
                const uniqueTypes = new Set(gameState.data.protocols.built.map(p => p.type));
                return uniqueTypes.size >= req.value;
            default:
                return false;
        }
    }

    unlockAchievement(achievementId) {
        const achievement = this.achievements.get(achievementId);
        if (!achievement) return;

        this.unlockedAchievements.add(achievementId);
        
        // Award rewards
        if (achievement.reward) {
            Object.entries(achievement.reward).forEach(([resource, amount]) => {
                if (resource === 'experience') {
                    gameState.addExperience(amount);
                } else {
                    gameState.addResource(resource, amount);
                }
            });
        }

        // Show achievement popup
        this.showAchievementPopup(achievement);
        
        // Play sound effect
        if (window.audioManager) {
            window.audioManager.playAchievementUnlock();
        }

        // Save progress
        this.saveProgress();

        console.log(`Achievement unlocked: ${achievement.name}`);
    }

    showAchievementPopup(achievement) {
        const popup = document.getElementById('achievement-popup');
        if (!popup) return;

        const icon = popup.querySelector('.achievement-icon');
        const title = popup.querySelector('.achievement-title');
        const description = popup.querySelector('.achievement-description');

        icon.textContent = achievement.icon;
        title.textContent = achievement.name;
        description.textContent = achievement.description;

        // Add rarity class
        popup.className = `achievement-popup ${achievement.rarity}`;
        popup.classList.remove('hidden');

        // Animate in
        popup.style.transform = 'translateY(-100px) scale(0.8)';
        popup.style.opacity = '0';
        
        setTimeout(() => {
            popup.style.transform = 'translateY(0) scale(1)';
            popup.style.opacity = '1';
        }, 100);

        // Auto-hide after 4 seconds
        setTimeout(() => {
            popup.style.transform = 'translateY(-100px) scale(0.8)';
            popup.style.opacity = '0';
            setTimeout(() => {
                popup.classList.add('hidden');
            }, 300);
        }, 4000);
    }

    // Daily Challenges
    generateDailyChallenge() {
        const templates = Array.from(this.dailyChallenges.values());
        const template = templates[Math.floor(Math.random() * templates.length)];
        
        const playerLevel = gameState ? gameState.data.player.level : 1;
        const difficultyMultiplier = 1 + (playerLevel - 1) * 0.1;
        const target = Math.ceil(template.baseTarget * difficultyMultiplier);

        this.currentChallenge = {
            ...template,
            target,
            progress: 0,
            startTime: Date.now(),
            endTime: Date.now() + (24 * 60 * 60 * 1000), // 24 hours
            description: template.description.replace('{target}', target)
        };

        this.updateDailyChallengeUI();
        this.saveChallengeProgress();
    }

    updateDailyChallengeProgress(type, amount) {
        if (!this.currentChallenge || this.currentChallenge.type !== type) return;
        if (Date.now() > this.currentChallenge.endTime) {
            this.generateDailyChallenge();
            return;
        }

        this.currentChallenge.progress += amount;
        
        if (this.currentChallenge.progress >= this.currentChallenge.target) {
            this.completeDailyChallenge();
        } else {
            this.updateDailyChallengeUI();
            this.saveChallengeProgress();
        }
    }

    completeDailyChallenge() {
        if (!this.currentChallenge) return;

        // Award rewards
        Object.entries(this.currentChallenge.reward).forEach(([resource, amount]) => {
            if (resource === 'experience') {
                gameState.addExperience(amount);
            } else {
                gameState.addResource(resource, amount);
            }
        });

        // Show completion notification
        if (window.uiManager) {
            window.uiManager.showNotification(
                `Daily Challenge Complete! ${this.currentChallenge.name}`,
                'success',
                4000
            );
        }

        // Play sound
        if (window.audioManager) {
            window.audioManager.playSound('questComplete');
        }

        // Generate new challenge
        setTimeout(() => {
            this.generateDailyChallenge();
        }, 2000);
    }

    updateDailyChallengeUI() {
        const challengeElement = document.getElementById('daily-challenge');
        if (!challengeElement || !this.currentChallenge) return;

        const title = challengeElement.querySelector('.challenge-title');
        const timer = challengeElement.querySelector('.challenge-timer');
        const progressText = challengeElement.querySelector('.progress-text');
        const progressFill = challengeElement.querySelector('.progress-fill');

        if (title) title.textContent = this.currentChallenge.name;
        
        if (timer) {
            const timeLeft = this.currentChallenge.endTime - Date.now();
            const hours = Math.floor(timeLeft / (1000 * 60 * 60));
            const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);
            timer.textContent = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }

        if (progressText) {
            progressText.textContent = `${this.currentChallenge.description} (${this.currentChallenge.progress}/${this.currentChallenge.target})`;
        }

        if (progressFill) {
            const percentage = Math.min(100, (this.currentChallenge.progress / this.currentChallenge.target) * 100);
            progressFill.style.width = `${percentage}%`;
        }
    }

    // Special Achievement Checks
    checkSpeedBuilding() {
        // Track protocols built in the last minute
        const now = Date.now();
        const oneMinuteAgo = now - 60000;
        
        if (!this.recentBuilds) this.recentBuilds = [];
        
        this.recentBuilds.push(now);
        this.recentBuilds = this.recentBuilds.filter(time => time > oneMinuteAgo);
        
        if (this.recentBuilds.length >= 5) {
            this.checkAchievement('speed_builder');
        }
    }

    // Save/Load System
    saveProgress() {
        const data = {
            unlockedAchievements: Array.from(this.unlockedAchievements),
            lastSave: Date.now()
        };
        localStorage.setItem('defi-dynasty-achievements', JSON.stringify(data));
    }

    saveChallengeProgress() {
        if (this.currentChallenge) {
            localStorage.setItem('defi-dynasty-daily-challenge', JSON.stringify(this.currentChallenge));
        }
    }

    loadProgress() {
        try {
            // Load achievements
            const achievementData = localStorage.getItem('defi-dynasty-achievements');
            if (achievementData) {
                const data = JSON.parse(achievementData);
                this.unlockedAchievements = new Set(data.unlockedAchievements || []);
            }

            // Load daily challenge
            const challengeData = localStorage.getItem('defi-dynasty-daily-challenge');
            if (challengeData) {
                const challenge = JSON.parse(challengeData);
                if (Date.now() < challenge.endTime) {
                    this.currentChallenge = challenge;
                    this.updateDailyChallengeUI();
                } else {
                    this.generateDailyChallenge();
                }
            } else {
                this.generateDailyChallenge();
            }
        } catch (error) {
            console.warn('Failed to load achievement progress:', error);
            this.generateDailyChallenge();
        }
    }

    // Public API
    getUnlockedAchievements() {
        return Array.from(this.unlockedAchievements).map(id => this.achievements.get(id));
    }

    getAchievementProgress(achievementId) {
        const achievement = this.achievements.get(achievementId);
        if (!achievement) return null;

        const isUnlocked = this.unlockedAchievements.has(achievementId);
        const progress = this.getProgressValue(achievement.requirement);
        
        return {
            achievement,
            isUnlocked,
            progress,
            percentage: Math.min(100, (progress / achievement.requirement.value) * 100)
        };
    }

    getProgressValue(requirement) {
        if (!gameState) return 0;

        const stats = gameState.data.statistics;
        
        switch (requirement.type) {
            case 'protocols_built':
                return stats.protocolsBuilt;
            case 'total_eth_earned':
                return stats.totalEthEarned;
            case 'player_level':
                return gameState.data.player.level;
            case 'upgrades_purchased':
                return stats.upgradesPurchased || 0;
            case 'unique_protocols':
                const uniqueTypes = new Set(gameState.data.protocols.built.map(p => p.type));
                return uniqueTypes.size;
            default:
                return 0;
        }
    }

    getCurrentChallenge() {
        return this.currentChallenge;
    }
}

// Global achievement system instance
window.achievementSystem = new AchievementSystem();

// Update daily challenge timer every second
setInterval(() => {
    if (window.achievementSystem && window.achievementSystem.currentChallenge) {
        window.achievementSystem.updateDailyChallengeUI();
    }
}, 1000);
