/**
 * Enhanced Particle System
 * Creates dynamic particle effects for enhanced visual feedback
 */

class ParticleSystem {
    constructor() {
        this.particles = [];
        this.particlePool = [];
        this.maxParticles = 500;
        this.canvas = null;
        this.ctx = null;
        this.animationFrame = null;
        this.isRunning = false;
        
        this.initializeCanvas();
        this.start();
    }

    initializeCanvas() {
        // Create particle canvas overlay
        this.canvas = document.createElement('canvas');
        this.canvas.id = 'particle-canvas';
        this.canvas.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1001;
        `;
        
        document.body.appendChild(this.canvas);
        this.ctx = this.canvas.getContext('2d');
        
        // Handle resize
        this.resize();
        window.addEventListener('resize', () => this.resize());
    }

    resize() {
        this.canvas.width = window.innerWidth;
        this.canvas.height = window.innerHeight;
    }

    start() {
        if (this.isRunning) return;
        this.isRunning = true;
        this.animate();
    }

    stop() {
        this.isRunning = false;
        if (this.animationFrame) {
            cancelAnimationFrame(this.animationFrame);
        }
    }

    animate() {
        if (!this.isRunning) return;
        
        this.update();
        this.render();
        
        this.animationFrame = requestAnimationFrame(() => this.animate());
    }

    update() {
        // Update all particles
        for (let i = this.particles.length - 1; i >= 0; i--) {
            const particle = this.particles[i];
            
            // Update position
            particle.x += particle.vx;
            particle.y += particle.vy;
            
            // Apply gravity if enabled
            if (particle.gravity) {
                particle.vy += particle.gravity;
            }
            
            // Apply drag
            if (particle.drag) {
                particle.vx *= particle.drag;
                particle.vy *= particle.drag;
            }
            
            // Update life
            particle.life -= particle.decay;
            particle.alpha = Math.max(0, particle.life / particle.maxLife);
            
            // Update size
            if (particle.scaleDecay) {
                particle.size *= particle.scaleDecay;
            }
            
            // Remove dead particles
            if (particle.life <= 0 || particle.size <= 0.1) {
                this.returnToPool(particle);
                this.particles.splice(i, 1);
            }
        }
    }

    render() {
        // Clear canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Render all particles
        this.particles.forEach(particle => {
            this.renderParticle(particle);
        });
    }

    renderParticle(particle) {
        this.ctx.save();
        
        // Set alpha
        this.ctx.globalAlpha = particle.alpha;
        
        // Move to particle position
        this.ctx.translate(particle.x, particle.y);
        
        // Rotate if needed
        if (particle.rotation) {
            this.ctx.rotate(particle.rotation);
            particle.rotation += particle.rotationSpeed || 0;
        }
        
        // Render based on type
        switch (particle.type) {
            case 'circle':
                this.renderCircle(particle);
                break;
            case 'square':
                this.renderSquare(particle);
                break;
            case 'star':
                this.renderStar(particle);
                break;
            case 'coin':
                this.renderCoin(particle);
                break;
            case 'energy':
                this.renderEnergy(particle);
                break;
            case 'sparkle':
                this.renderSparkle(particle);
                break;
            default:
                this.renderCircle(particle);
        }
        
        this.ctx.restore();
    }

    renderCircle(particle) {
        this.ctx.beginPath();
        this.ctx.arc(0, 0, particle.size, 0, Math.PI * 2);
        this.ctx.fillStyle = particle.color;
        this.ctx.fill();
        
        if (particle.glow) {
            this.ctx.shadowBlur = particle.size * 2;
            this.ctx.shadowColor = particle.color;
            this.ctx.fill();
        }
    }

    renderSquare(particle) {
        const size = particle.size;
        this.ctx.fillStyle = particle.color;
        this.ctx.fillRect(-size/2, -size/2, size, size);
        
        if (particle.glow) {
            this.ctx.shadowBlur = size;
            this.ctx.shadowColor = particle.color;
            this.ctx.fillRect(-size/2, -size/2, size, size);
        }
    }

    renderStar(particle) {
        const size = particle.size;
        const spikes = 5;
        const outerRadius = size;
        const innerRadius = size * 0.4;
        
        this.ctx.beginPath();
        for (let i = 0; i < spikes * 2; i++) {
            const radius = i % 2 === 0 ? outerRadius : innerRadius;
            const angle = (i * Math.PI) / spikes;
            const x = Math.cos(angle) * radius;
            const y = Math.sin(angle) * radius;
            
            if (i === 0) {
                this.ctx.moveTo(x, y);
            } else {
                this.ctx.lineTo(x, y);
            }
        }
        this.ctx.closePath();
        this.ctx.fillStyle = particle.color;
        this.ctx.fill();
    }

    renderCoin(particle) {
        const size = particle.size;
        
        // Outer ring
        this.ctx.beginPath();
        this.ctx.arc(0, 0, size, 0, Math.PI * 2);
        this.ctx.fillStyle = '#ffd700';
        this.ctx.fill();
        
        // Inner circle
        this.ctx.beginPath();
        this.ctx.arc(0, 0, size * 0.7, 0, Math.PI * 2);
        this.ctx.fillStyle = '#ffed4e';
        this.ctx.fill();
        
        // Shine effect
        this.ctx.beginPath();
        this.ctx.arc(-size * 0.3, -size * 0.3, size * 0.3, 0, Math.PI * 2);
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
        this.ctx.fill();
    }

    renderEnergy(particle) {
        const size = particle.size;
        
        // Lightning bolt shape
        this.ctx.beginPath();
        this.ctx.moveTo(-size * 0.3, -size);
        this.ctx.lineTo(size * 0.1, -size * 0.2);
        this.ctx.lineTo(-size * 0.1, -size * 0.2);
        this.ctx.lineTo(size * 0.3, size);
        this.ctx.lineTo(-size * 0.1, size * 0.2);
        this.ctx.lineTo(size * 0.1, size * 0.2);
        this.ctx.closePath();
        
        this.ctx.fillStyle = particle.color;
        this.ctx.fill();
        
        // Glow effect
        this.ctx.shadowBlur = size;
        this.ctx.shadowColor = particle.color;
        this.ctx.fill();
    }

    renderSparkle(particle) {
        const size = particle.size;
        
        // Cross shape
        this.ctx.strokeStyle = particle.color;
        this.ctx.lineWidth = 2;
        this.ctx.lineCap = 'round';
        
        this.ctx.beginPath();
        this.ctx.moveTo(-size, 0);
        this.ctx.lineTo(size, 0);
        this.ctx.moveTo(0, -size);
        this.ctx.lineTo(0, size);
        this.ctx.stroke();
        
        // Diagonal lines
        this.ctx.beginPath();
        this.ctx.moveTo(-size * 0.7, -size * 0.7);
        this.ctx.lineTo(size * 0.7, size * 0.7);
        this.ctx.moveTo(size * 0.7, -size * 0.7);
        this.ctx.lineTo(-size * 0.7, size * 0.7);
        this.ctx.stroke();
    }

    // Particle creation methods
    createParticle(options) {
        let particle = this.getFromPool();
        
        // Default properties
        Object.assign(particle, {
            x: 0,
            y: 0,
            vx: 0,
            vy: 0,
            size: 5,
            color: '#ffffff',
            alpha: 1,
            life: 1,
            maxLife: 1,
            decay: 0.01,
            type: 'circle',
            gravity: 0,
            drag: 1,
            rotation: 0,
            rotationSpeed: 0,
            scaleDecay: 1,
            glow: false
        }, options);
        
        this.particles.push(particle);
        return particle;
    }

    // Preset particle effects
    createCoinBurst(x, y, count = 10) {
        for (let i = 0; i < count; i++) {
            const angle = (Math.PI * 2 * i) / count;
            const speed = 2 + Math.random() * 3;
            
            this.createParticle({
                x: x,
                y: y,
                vx: Math.cos(angle) * speed,
                vy: Math.sin(angle) * speed,
                size: 8 + Math.random() * 4,
                type: 'coin',
                life: 1,
                decay: 0.008,
                gravity: 0.1,
                drag: 0.98,
                rotationSpeed: 0.1
            });
        }
    }

    createEnergyPulse(x, y, count = 15) {
        for (let i = 0; i < count; i++) {
            const angle = Math.random() * Math.PI * 2;
            const speed = 1 + Math.random() * 2;
            
            this.createParticle({
                x: x,
                y: y,
                vx: Math.cos(angle) * speed,
                vy: Math.sin(angle) * speed,
                size: 6 + Math.random() * 3,
                color: '#ff6b6b',
                type: 'energy',
                life: 1,
                decay: 0.01,
                glow: true
            });
        }
    }

    createLevelUpExplosion(x, y) {
        // Stars
        for (let i = 0; i < 20; i++) {
            const angle = Math.random() * Math.PI * 2;
            const speed = 3 + Math.random() * 5;
            
            this.createParticle({
                x: x,
                y: y,
                vx: Math.cos(angle) * speed,
                vy: Math.sin(angle) * speed,
                size: 8 + Math.random() * 6,
                color: '#ffd700',
                type: 'star',
                life: 1.5,
                decay: 0.006,
                gravity: 0.05,
                drag: 0.99,
                rotationSpeed: 0.2,
                glow: true
            });
        }
        
        // Sparkles
        for (let i = 0; i < 30; i++) {
            const angle = Math.random() * Math.PI * 2;
            const speed = 2 + Math.random() * 4;
            
            this.createParticle({
                x: x + (Math.random() - 0.5) * 40,
                y: y + (Math.random() - 0.5) * 40,
                vx: Math.cos(angle) * speed,
                vy: Math.sin(angle) * speed,
                size: 4 + Math.random() * 3,
                color: Math.random() > 0.5 ? '#00d4ff' : '#4ecdc4',
                type: 'sparkle',
                life: 1,
                decay: 0.008,
                gravity: -0.02
            });
        }
    }

    createProtocolBuildEffect(x, y, protocolType) {
        const colors = {
            amm: '#00d4ff',
            lending: '#4ecdc4',
            staking: '#ffe66d',
            governance: '#a8e6cf'
        };
        
        const color = colors[protocolType] || '#ffffff';
        
        // Main burst
        for (let i = 0; i < 25; i++) {
            const angle = Math.random() * Math.PI * 2;
            const speed = 2 + Math.random() * 4;
            
            this.createParticle({
                x: x,
                y: y,
                vx: Math.cos(angle) * speed,
                vy: Math.sin(angle) * speed,
                size: 5 + Math.random() * 4,
                color: color,
                type: 'circle',
                life: 1,
                decay: 0.01,
                gravity: 0.08,
                drag: 0.98,
                glow: true
            });
        }
        
        // Secondary sparkles
        for (let i = 0; i < 15; i++) {
            const angle = Math.random() * Math.PI * 2;
            const speed = 1 + Math.random() * 2;
            
            this.createParticle({
                x: x + (Math.random() - 0.5) * 30,
                y: y + (Math.random() - 0.5) * 30,
                vx: Math.cos(angle) * speed,
                vy: Math.sin(angle) * speed,
                size: 3 + Math.random() * 2,
                color: '#ffffff',
                type: 'sparkle',
                life: 0.8,
                decay: 0.012,
                gravity: -0.01
            });
        }
    }

    createResourceFlow(fromX, fromY, toX, toY, resourceType, amount) {
        const colors = {
            eth: '#00d4ff',
            tokens: '#ffe66d',
            energy: '#ff6b6b'
        };
        
        const color = colors[resourceType] || '#ffffff';
        const particleCount = Math.min(20, Math.max(5, Math.floor(amount / 50)));
        
        for (let i = 0; i < particleCount; i++) {
            const progress = i / particleCount;
            const x = fromX + (toX - fromX) * progress;
            const y = fromY + (toY - fromY) * progress;
            
            setTimeout(() => {
                this.createParticle({
                    x: x + (Math.random() - 0.5) * 20,
                    y: y + (Math.random() - 0.5) * 20,
                    vx: (toX - fromX) * 0.02,
                    vy: (toY - fromY) * 0.02,
                    size: 4 + Math.random() * 3,
                    color: color,
                    type: resourceType === 'energy' ? 'energy' : 'circle',
                    life: 1,
                    decay: 0.015,
                    glow: true
                });
            }, i * 50);
        }
    }

    // Pool management
    getFromPool() {
        return this.particlePool.pop() || {};
    }

    returnToPool(particle) {
        // Reset particle
        Object.keys(particle).forEach(key => {
            delete particle[key];
        });
        
        this.particlePool.push(particle);
    }

    // Public API
    clear() {
        this.particles.forEach(particle => this.returnToPool(particle));
        this.particles.length = 0;
    }

    destroy() {
        this.stop();
        if (this.canvas && this.canvas.parentNode) {
            this.canvas.parentNode.removeChild(this.canvas);
        }
    }
}

// Global particle system instance
window.particleSystem = new ParticleSystem();

// Integration with game events
document.addEventListener('game:protocolBuilt', (e) => {
    const protocol = e.detail;
    // Get position from Phaser or fallback to center
    const x = window.innerWidth / 2;
    const y = window.innerHeight / 2;
    
    window.particleSystem.createProtocolBuildEffect(x, y, protocol.type);
});

document.addEventListener('game:levelUp', (e) => {
    const levelElement = document.getElementById('player-level');
    if (levelElement) {
        const rect = levelElement.getBoundingClientRect();
        window.particleSystem.createLevelUpExplosion(
            rect.left + rect.width / 2,
            rect.top + rect.height / 2
        );
    }
});

document.addEventListener('game:resourceChanged', (e) => {
    const data = e.detail;
    if (data.amount > 0 && data.amount >= 100) {
        const resourceElement = document.querySelector(`.${data.type}-resource`);
        if (resourceElement) {
            const rect = resourceElement.getBoundingClientRect();
            
            if (data.type === 'eth') {
                window.particleSystem.createCoinBurst(
                    rect.left + rect.width / 2,
                    rect.top + rect.height / 2,
                    Math.min(15, Math.floor(data.amount / 100))
                );
            } else if (data.type === 'energy') {
                window.particleSystem.createEnergyPulse(
                    rect.left + rect.width / 2,
                    rect.top + rect.height / 2,
                    Math.min(10, Math.floor(data.amount / 10))
                );
            }
        }
    }
});
