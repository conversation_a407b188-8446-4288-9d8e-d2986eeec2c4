/**
 * Enhanced UI Manager
 * Advanced animations, particle effects, and premium UI interactions
 */

class EnhancedUIManager {
    constructor() {
        this.activeAnimations = new Map();
        this.floatingTexts = [];
        this.resourceBars = new Map();
        this.tooltips = new Map();
        this.modalStack = [];
        
        this.initializeEnhancedFeatures();
        this.setupAdvancedEventListeners();
    }

    initializeEnhancedFeatures() {
        this.createAdvancedTooltips();
        this.initializeResourceBars();
        this.setupRippleEffects();
        this.createFloatingTextSystem();
        this.initializeModalSystem();
    }

    setupAdvancedEventListeners() {
        // Enhanced button interactions
        document.addEventListener('mouseenter', (e) => {
            if (e.target.matches('.menu-btn, .hud-btn, .protocol-tab')) {
                this.addButtonHoverEffect(e.target);
                if (window.audioManager) {
                    window.audioManager.playButtonHover();
                }
            }
        }, true);

        document.addEventListener('mouseleave', (e) => {
            if (e.target.matches('.menu-btn, .hud-btn, .protocol-tab')) {
                this.removeButtonHoverEffect(e.target);
            }
        }, true);

        document.addEventListener('click', (e) => {
            if (e.target.matches('.menu-btn, .hud-btn, .protocol-tab, .build-btn')) {
                this.addRippleEffect(e.target, e);
                if (window.audioManager) {
                    window.audioManager.playButtonClick();
                }
            }
        }, true);

        // Resource change animations
        document.addEventListener('game:resourceChanged', (e) => this.onResourceChanged(e.detail));
        document.addEventListener('game:levelUp', (e) => this.onLevelUp(e.detail));
        document.addEventListener('game:protocolBuilt', (e) => this.onProtocolBuilt(e.detail));
        document.addEventListener('game:protocolEarned', (e) => this.onProtocolEarned(e.detail));
    }

    createAdvancedTooltips() {
        // Create enhanced tooltip system
        this.tooltip = document.createElement('div');
        this.tooltip.className = 'enhanced-tooltip';
        this.tooltip.innerHTML = `
            <div class="tooltip-content">
                <div class="tooltip-title"></div>
                <div class="tooltip-description"></div>
                <div class="tooltip-stats"></div>
            </div>
            <div class="tooltip-arrow"></div>
        `;
        document.body.appendChild(this.tooltip);

        // Setup tooltip event listeners
        document.addEventListener('mouseover', (e) => {
            const element = e.target.closest('[data-tooltip]');
            if (element) {
                this.showEnhancedTooltip(element, e);
            }
        });

        document.addEventListener('mouseout', (e) => {
            const element = e.target.closest('[data-tooltip]');
            if (element) {
                this.hideEnhancedTooltip();
            }
        });

        document.addEventListener('mousemove', (e) => {
            if (this.tooltip.style.opacity === '1') {
                this.updateTooltipPosition(e);
            }
        });
    }

    initializeResourceBars() {
        const resources = ['eth', 'token', 'energy', 'xp'];
        resources.forEach(resource => {
            const bar = document.getElementById(`${resource}-fill`);
            if (bar) {
                this.resourceBars.set(resource, {
                    element: bar,
                    currentWidth: 0,
                    targetWidth: 0,
                    animating: false
                });
            }
        });
    }

    setupRippleEffects() {
        // Add ripple containers to buttons that don't have them
        document.querySelectorAll('.menu-btn, .hud-btn, .build-btn').forEach(button => {
            if (!button.querySelector('.btn-ripple')) {
                const ripple = document.createElement('div');
                ripple.className = 'btn-ripple';
                button.appendChild(ripple);
            }
        });
    }

    createFloatingTextSystem() {
        this.floatingTextContainer = document.getElementById('floating-texts');
        if (!this.floatingTextContainer) {
            this.floatingTextContainer = document.createElement('div');
            this.floatingTextContainer.id = 'floating-texts';
            this.floatingTextContainer.className = 'floating-texts';
            document.body.appendChild(this.floatingTextContainer);
        }
    }

    initializeModalSystem() {
        // Enhanced modal management with stacking and transitions
        this.modalOverlay = document.createElement('div');
        this.modalOverlay.className = 'modal-overlay';
        this.modalOverlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(5px);
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        `;
        document.body.appendChild(this.modalOverlay);
    }

    // Enhanced Button Effects
    addButtonHoverEffect(button) {
        if (this.activeAnimations.has(button)) return;

        const animation = button.animate([
            { transform: 'translateY(0) scale(1)', boxShadow: button.style.boxShadow || 'none' },
            { transform: 'translateY(-2px) scale(1.02)', boxShadow: '0 8px 25px rgba(0, 212, 255, 0.3)' }
        ], {
            duration: 200,
            easing: 'ease-out',
            fill: 'forwards'
        });

        this.activeAnimations.set(button, animation);
    }

    removeButtonHoverEffect(button) {
        const animation = this.activeAnimations.get(button);
        if (animation) {
            animation.reverse();
            setTimeout(() => {
                this.activeAnimations.delete(button);
            }, 200);
        }
    }

    addRippleEffect(button, event) {
        const ripple = button.querySelector('.btn-ripple');
        if (!ripple) return;

        const rect = button.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;

        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = x + 'px';
        ripple.style.top = y + 'px';
        ripple.style.transform = 'scale(0)';
        ripple.style.opacity = '0.6';

        ripple.animate([
            { transform: 'scale(0)', opacity: '0.6' },
            { transform: 'scale(1)', opacity: '0' }
        ], {
            duration: 600,
            easing: 'ease-out'
        });
    }

    // Enhanced Tooltip System
    showEnhancedTooltip(element, event) {
        const tooltipData = this.getTooltipData(element);
        if (!tooltipData) return;

        const titleEl = this.tooltip.querySelector('.tooltip-title');
        const descEl = this.tooltip.querySelector('.tooltip-description');
        const statsEl = this.tooltip.querySelector('.tooltip-stats');

        titleEl.textContent = tooltipData.title || '';
        descEl.textContent = tooltipData.description || '';
        statsEl.innerHTML = tooltipData.stats || '';

        this.tooltip.style.opacity = '1';
        this.tooltip.style.visibility = 'visible';
        this.updateTooltipPosition(event);
    }

    hideEnhancedTooltip() {
        this.tooltip.style.opacity = '0';
        this.tooltip.style.visibility = 'hidden';
    }

    getTooltipData(element) {
        const basicTooltip = element.dataset.tooltip;
        if (!basicTooltip) return null;

        // Enhanced tooltip data based on element type
        if (element.classList.contains('protocol-tab')) {
            const protocolType = element.dataset.protocol;
            const protocolDef = window.protocolManager?.protocolTypes[protocolType];
            if (protocolDef) {
                return {
                    title: protocolDef.name,
                    description: protocolDef.description,
                    stats: `Cost: ${protocolDef.baseCost.eth} ETH, ${protocolDef.baseCost.energy} Energy<br>
                           Earning: ${protocolDef.baseEarning} ${protocolDef.earningType}/cycle`
                };
            }
        }

        if (element.classList.contains('resource')) {
            const resourceType = element.className.match(/(\w+)-resource/)?.[1];
            if (resourceType && window.gameState) {
                const current = window.gameState.getResource(resourceType);
                return {
                    title: resourceType.toUpperCase(),
                    description: basicTooltip,
                    stats: `Current: ${current}`
                };
            }
        }

        return { description: basicTooltip };
    }

    updateTooltipPosition(event) {
        const tooltipRect = this.tooltip.getBoundingClientRect();
        const windowWidth = window.innerWidth;
        const windowHeight = window.innerHeight;

        let left = event.clientX + 15;
        let top = event.clientY - tooltipRect.height - 15;

        // Adjust if tooltip goes off screen
        if (left + tooltipRect.width > windowWidth) {
            left = event.clientX - tooltipRect.width - 15;
        }
        if (top < 0) {
            top = event.clientY + 15;
        }

        this.tooltip.style.left = left + 'px';
        this.tooltip.style.top = top + 'px';
    }

    // Enhanced Resource Bar Animations
    updateResourceBar(resourceType, percentage, animated = true) {
        const barData = this.resourceBars.get(resourceType);
        if (!barData) return;

        barData.targetWidth = Math.max(0, Math.min(100, percentage));

        if (animated && !barData.animating) {
            this.animateResourceBar(barData);
        } else if (!animated) {
            barData.element.style.width = barData.targetWidth + '%';
            barData.currentWidth = barData.targetWidth;
        }
    }

    animateResourceBar(barData) {
        barData.animating = true;
        const startWidth = barData.currentWidth;
        const targetWidth = barData.targetWidth;
        const duration = 500;
        const startTime = performance.now();

        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // Easing function
            const easeProgress = 1 - Math.pow(1 - progress, 3);
            
            const currentWidth = startWidth + (targetWidth - startWidth) * easeProgress;
            barData.element.style.width = currentWidth + '%';
            barData.currentWidth = currentWidth;

            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                barData.animating = false;
            }
        };

        requestAnimationFrame(animate);
    }

    // Enhanced Floating Text System
    showFloatingText(text, x, y, options = {}) {
        const floatingText = document.createElement('div');
        floatingText.className = `floating-text ${options.className || ''}`;
        floatingText.textContent = text;
        
        const defaultStyle = {
            position: 'fixed',
            left: x + 'px',
            top: y + 'px',
            pointerEvents: 'none',
            zIndex: '10002',
            fontSize: options.fontSize || '1.2rem',
            fontWeight: 'bold',
            color: options.color || '#00d4ff',
            textShadow: '0 0 10px currentColor',
            transform: 'translateX(-50%)',
            transition: 'all 0.1s ease'
        };

        Object.assign(floatingText.style, defaultStyle, options.style || {});
        
        this.floatingTextContainer.appendChild(floatingText);
        this.floatingTexts.push(floatingText);

        // Animate
        this.animateFloatingText(floatingText, options);

        // Remove after animation
        setTimeout(() => {
            this.removeFloatingText(floatingText);
        }, options.duration || 2000);
    }

    animateFloatingText(element, options) {
        const startY = parseInt(element.style.top);
        const endY = startY - (options.distance || 100);
        const duration = options.duration || 2000;

        element.animate([
            { 
                top: startY + 'px', 
                opacity: '1', 
                transform: 'translateX(-50%) scale(1)' 
            },
            { 
                top: endY + 'px', 
                opacity: '0', 
                transform: 'translateX(-50%) scale(0.8)' 
            }
        ], {
            duration: duration,
            easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)'
        });
    }

    removeFloatingText(floatingText) {
        if (floatingText.parentNode) {
            floatingText.remove();
            const index = this.floatingTexts.indexOf(floatingText);
            if (index > -1) {
                this.floatingTexts.splice(index, 1);
            }
        }
    }

    // Enhanced Notification System
    showEnhancedNotification(message, type = 'info', options = {}) {
        const notification = document.createElement('div');
        notification.className = `enhanced-notification ${type}`;
        
        const icon = this.getNotificationIcon(type);
        notification.innerHTML = `
            <div class="notification-icon">${icon}</div>
            <div class="notification-content">
                <div class="notification-message">${message}</div>
                ${options.subtitle ? `<div class="notification-subtitle">${options.subtitle}</div>` : ''}
            </div>
            <div class="notification-close">×</div>
            <div class="notification-progress"></div>
        `;

        // Add to container
        const container = document.getElementById('notifications');
        container.appendChild(notification);

        // Setup close button
        notification.querySelector('.notification-close').addEventListener('click', () => {
            this.removeNotification(notification);
        });

        // Animate in
        notification.style.transform = 'translateX(100%)';
        notification.style.opacity = '0';
        
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
            notification.style.opacity = '1';
        }, 100);

        // Auto-remove with progress bar
        const duration = options.duration || 4000;
        const progressBar = notification.querySelector('.notification-progress');
        
        progressBar.animate([
            { width: '100%' },
            { width: '0%' }
        ], {
            duration: duration,
            easing: 'linear'
        });

        setTimeout(() => {
            this.removeNotification(notification);
        }, duration);

        return notification;
    }

    getNotificationIcon(type) {
        const icons = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️',
            achievement: '🏆'
        };
        return icons[type] || icons.info;
    }

    removeNotification(notification) {
        notification.style.transform = 'translateX(100%)';
        notification.style.opacity = '0';
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 300);
    }

    // Event Handlers
    onResourceChanged(data) {
        if (data.amount > 0) {
            // Find resource element for position
            const resourceElement = document.querySelector(`.${data.type}-resource`);
            if (resourceElement) {
                const rect = resourceElement.getBoundingClientRect();
                this.showFloatingText(
                    `+${StringUtils.formatNumber(data.amount)}`,
                    rect.left + rect.width / 2,
                    rect.top,
                    {
                        color: this.getResourceColor(data.type),
                        className: 'resource-gain',
                        fontSize: '1rem'
                    }
                );
            }

            // Update resource bar
            if (data.type === 'energy') {
                const percentage = (data.total / window.gameState.data.resources.maxEnergy) * 100;
                this.updateResourceBar('energy', percentage);
            }
        }

        // Update resource pulse effect
        this.addResourcePulse(data.type);
    }

    onLevelUp(data) {
        // Dramatic level up effect
        const levelElement = document.getElementById('player-level');
        if (levelElement) {
            const rect = levelElement.getBoundingClientRect();
            
            // Multiple floating texts
            this.showFloatingText(
                'LEVEL UP!',
                rect.left + rect.width / 2,
                rect.top - 20,
                {
                    color: '#ffd700',
                    fontSize: '1.8rem',
                    className: 'level-up-main',
                    duration: 3000,
                    distance: 150
                }
            );
            
            this.showFloatingText(
                `Level ${data.level}`,
                rect.left + rect.width / 2,
                rect.top + 20,
                {
                    color: '#00d4ff',
                    fontSize: '1.2rem',
                    className: 'level-up-number',
                    duration: 2500,
                    distance: 100
                }
            );

            // Screen flash effect
            this.createScreenFlash('#ffd700', 0.3, 500);
        }

        // Update XP bar
        const xpPercentage = (window.gameState.data.player.experience / window.gameState.data.player.experienceToNext) * 100;
        this.updateResourceBar('xp', xpPercentage);
    }

    onProtocolBuilt(protocol) {
        // Protocol build celebration
        if (window.phaserGameManager) {
            // Phaser will handle the visual effects
        } else {
            // Fallback for non-Phaser mode
            this.showEnhancedNotification(
                `${window.protocolManager.protocolTypes[protocol.type].name} built!`,
                'success',
                { subtitle: 'Your DeFi empire grows stronger!' }
            );
        }
    }

    onProtocolEarned(data) {
        // Show earning particles if Phaser is available
        if (window.phaserGameManager) {
            window.phaserGameManager.showProtocolEarning(data.protocolId, data.earning, data.type);
        }
    }

    // Utility Methods
    getResourceColor(resourceType) {
        const colors = {
            eth: '#00d4ff',
            tokens: '#ffe66d',
            energy: '#ff6b6b',
            governance: '#a8e6cf'
        };
        return colors[resourceType] || '#ffffff';
    }

    addResourcePulse(resourceType) {
        const resourceElement = document.querySelector(`.${resourceType}-resource .resource-pulse`);
        if (resourceElement) {
            resourceElement.style.animation = 'none';
            setTimeout(() => {
                resourceElement.style.animation = 'resourcePulse 0.5s ease-out';
            }, 10);
        }
    }

    createScreenFlash(color, opacity, duration) {
        const flash = document.createElement('div');
        flash.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: ${color};
            opacity: 0;
            pointer-events: none;
            z-index: 10001;
        `;
        
        document.body.appendChild(flash);
        
        flash.animate([
            { opacity: 0 },
            { opacity: opacity },
            { opacity: 0 }
        ], {
            duration: duration,
            easing: 'ease-out'
        }).onfinish = () => {
            flash.remove();
        };
    }

    // Public API
    updateResourceDisplay() {
        if (!window.gameState) return;

        const resources = window.gameState.data.resources;
        
        // Update text displays
        document.getElementById('eth-balance').textContent = StringUtils.formatNumber(resources.eth);
        document.getElementById('token-balance').textContent = StringUtils.formatNumber(resources.tokens);
        document.getElementById('energy-balance').textContent = `${resources.energy}/${resources.maxEnergy}`;
        document.getElementById('player-level').textContent = window.gameState.data.player.level;

        // Update resource bars
        this.updateResourceBar('energy', (resources.energy / resources.maxEnergy) * 100);
        
        const xpPercentage = (window.gameState.data.player.experience / window.gameState.data.player.experienceToNext) * 100;
        this.updateResourceBar('xp', xpPercentage);

        // Update Phaser display if available
        if (window.phaserGameManager) {
            window.phaserGameManager.updateResourceDisplay(resources);
        }
    }
}

// Global enhanced UI manager instance
window.enhancedUI = new EnhancedUIManager();
