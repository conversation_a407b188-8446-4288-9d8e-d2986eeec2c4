/**
 * Enhanced Game Controller
 * Integrates all enhanced systems for premium gameplay experience
 */

class EnhancedGame {
    constructor() {
        this.currentScreen = 'loading';
        this.isInitialized = false;
        this.selectedProtocolType = 'amm';
        this.gridSize = { width: 8, height: 6 };
        this.occupiedCells = new Set();
        this.gameSpeed = 1;
        this.isPaused = false;
        this.loadingTips = [
            "💡 Tip: Start with AMM protocols for steady income",
            "🚀 Tip: Upgrade efficiency first for better returns",
            "⚡ Tip: Manage your energy wisely - it regenerates over time",
            "🏆 Tip: Complete daily challenges for bonus rewards",
            "💰 Tip: Diversify your protocols to maximize earnings",
            "🎯 Tip: Check achievements for additional goals"
        ];
        
        this.init();
    }

    async init() {
        console.log('Initializing Enhanced DeFi Dynasty...');
        
        // Show enhanced loading screen
        this.showScreen('loading');
        await this.simulateEnhancedLoading();
        
        // Initialize all systems
        await this.initializeSystems();
        
        // Setup event listeners
        this.setupEnhancedEventListeners();
        
        // Check for existing save data and update menu
        this.updateMainMenuStats();
        
        // Show main menu
        if (gameState.hasSaveData()) {
            this.showScreen('main-menu');
            document.getElementById('continue-btn').style.display = 'block';
        } else {
            this.showScreen('main-menu');
            document.getElementById('continue-btn').style.display = 'none';
        }
        
        // Start background music
        if (window.audioManager) {
            setTimeout(() => {
                window.audioManager.playMusic();
            }, 1000);
        }
        
        this.isInitialized = true;
        console.log('Enhanced DeFi Dynasty initialized successfully!');
    }

    async simulateEnhancedLoading() {
        const loadingProgress = document.querySelector('.loading-progress');
        const loadingText = document.querySelector('.loading-text');
        const loadingPercentage = document.querySelector('.loading-percentage');
        const tipText = document.querySelector('.tip-text');
        
        const steps = [
            { progress: 15, text: 'Connecting to blockchain networks...', tip: 0 },
            { progress: 30, text: 'Loading smart contract templates...', tip: 1 },
            { progress: 45, text: 'Initializing DeFi protocols...', tip: 2 },
            { progress: 60, text: 'Setting up liquidity pools...', tip: 3 },
            { progress: 75, text: 'Configuring yield farming...', tip: 4 },
            { progress: 90, text: 'Preparing governance systems...', tip: 5 },
            { progress: 100, text: 'Ready to build your empire!', tip: null }
        ];

        for (const step of steps) {
            loadingProgress.style.width = `${step.progress}%`;
            loadingText.textContent = step.text;
            loadingPercentage.textContent = `${step.progress}%`;
            
            if (step.tip !== null) {
                tipText.textContent = this.loadingTips[step.tip];
            }
            
            await this.sleep(600 + Math.random() * 400); // Variable timing for realism
        }
    }

    async initializeSystems() {
        // Wait for all enhanced systems to be ready
        const systems = [
            window.audioManager,
            window.achievementSystem,
            window.phaserGameManager,
            window.enhancedUI
        ];

        // Give systems time to initialize
        await this.sleep(500);
        
        console.log('All enhanced systems initialized');
    }

    setupEnhancedEventListeners() {
        // Enhanced menu buttons
        document.getElementById('new-game-btn').addEventListener('click', () => this.startNewGame());
        document.getElementById('continue-btn').addEventListener('click', () => this.continueGame());
        document.getElementById('tutorial-btn').addEventListener('click', () => this.startTutorial());
        document.getElementById('achievements-btn').addEventListener('click', () => this.showAchievements());
        document.getElementById('leaderboard-btn').addEventListener('click', () => this.showLeaderboard());
        
        // Enhanced HUD buttons
        document.getElementById('pause-btn').addEventListener('click', () => this.togglePause());
        document.getElementById('settings-btn').addEventListener('click', () => this.showSettings());
        document.getElementById('shop-btn').addEventListener('click', () => this.showShop());
        document.getElementById('achievements-hud-btn').addEventListener('click', () => this.showAchievements());
        
        // Menu footer buttons
        document.getElementById('settings-menu-btn').addEventListener('click', () => this.showSettings());
        document.getElementById('sound-toggle-btn').addEventListener('click', () => this.toggleSound());
        document.getElementById('fullscreen-btn').addEventListener('click', () => this.toggleFullscreen());
        
        // Protocol tabs with enhanced effects
        document.querySelectorAll('.protocol-tab').forEach(tab => {
            tab.addEventListener('click', (e) => this.selectProtocolType(e.target.dataset.protocol));
        });

        // Building interface
        document.getElementById('confirm-build').addEventListener('click', () => this.confirmBuild());
        document.querySelector('.close-interface').addEventListener('click', () => this.closeBuildingInterface());

        // Enhanced game state events
        document.addEventListener('game:resourceChanged', (e) => this.onResourceChanged(e.detail));
        document.addEventListener('game:levelUp', (e) => this.onLevelUp(e.detail));
        document.addEventListener('game:protocolBuilt', (e) => this.onProtocolBuilt(e.detail));
        document.addEventListener('game:protocolEarned', (e) => this.onProtocolEarned(e.detail));
        
        // Window events
        window.addEventListener('beforeunload', () => this.saveGame());
        window.addEventListener('blur', () => this.saveGame());
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => this.handleKeyboardShortcuts(e));
        
        // Fullscreen change events
        document.addEventListener('fullscreenchange', () => this.onFullscreenChange());
    }

    handleKeyboardShortcuts(event) {
        if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
            return; // Don't handle shortcuts when typing
        }

        switch (event.key.toLowerCase()) {
            case 'escape':
                if (this.currentScreen === 'game-world') {
                    this.togglePause();
                }
                break;
            case ' ':
                event.preventDefault();
                this.togglePause();
                break;
            case 's':
                if (event.ctrlKey || event.metaKey) {
                    event.preventDefault();
                    this.saveGame();
                }
                break;
            case 'm':
                this.toggleSound();
                break;
            case 'f11':
                event.preventDefault();
                this.toggleFullscreen();
                break;
            case '1':
            case '2':
            case '3':
            case '4':
                const tabIndex = parseInt(event.key) - 1;
                const tabs = document.querySelectorAll('.protocol-tab');
                if (tabs[tabIndex] && !tabs[tabIndex].disabled) {
                    tabs[tabIndex].click();
                }
                break;
        }
    }

    // Enhanced Screen Management
    showScreen(screenId) {
        // Hide all screens with enhanced transitions
        document.querySelectorAll('.screen').forEach(screen => {
            if (screen.classList.contains('active')) {
                screen.style.transform = 'scale(0.95)';
                screen.style.opacity = '0';
                setTimeout(() => {
                    screen.classList.remove('active');
                }, 300);
            }
        });
        
        // Show target screen with enhanced transition
        setTimeout(() => {
            const targetScreen = document.getElementById(screenId);
            if (targetScreen) {
                targetScreen.classList.add('active');
                setTimeout(() => {
                    targetScreen.style.transform = 'scale(1)';
                    targetScreen.style.opacity = '1';
                }, 50);
                this.currentScreen = screenId;
            }

            // Show/hide HUD based on screen
            const hud = document.getElementById('game-hud');
            if (screenId === 'game-world') {
                hud.classList.remove('hidden');
                this.startGameLoop();
            } else {
                hud.classList.add('hidden');
                this.stopGameLoop();
            }
        }, 300);
    }

    // Enhanced Game Flow
    startNewGame() {
        // Show confirmation if there's existing progress
        if (gameState.hasSaveData()) {
            const confirmed = confirm('Starting a new game will erase your current progress. Are you sure?');
            if (!confirmed) return;
        }

        // Reset all systems
        gameState.reset();
        if (window.achievementSystem) {
            window.achievementSystem.generateDailyChallenge();
        }
        
        this.startGame();
    }

    continueGame() {
        this.startGame();
        this.loadGameState();
    }

    startGame() {
        this.showScreen('game-world');
        this.updateUI();
        
        // Start tutorial if not completed
        if (!gameState.data.tutorial.completed && !gameState.data.tutorial.skipped) {
            setTimeout(() => {
                this.startTutorial();
            }, 1000);
        }
        
        // Initialize Phaser game world
        if (window.phaserGameManager && window.phaserGameManager.isInitialized) {
            this.loadProtocolsIntoPhaser();
        }
    }

    startTutorial() {
        if (window.tutorialManager) {
            window.tutorialManager.startTutorial();
        }
    }

    loadGameState() {
        // Restore built protocols
        const protocols = gameState.getProtocols();
        protocols.forEach(protocol => {
            this.addProtocolToGame(protocol);
            protocolManager.activateProtocol(protocol);
        });
    }

    loadProtocolsIntoPhaser() {
        if (!window.phaserGameManager) return;
        
        const protocols = gameState.getProtocols();
        protocols.forEach(protocol => {
            window.phaserGameManager.addProtocolToGrid(protocol);
        });
    }

    // Enhanced Protocol Management
    selectProtocolType(type) {
        this.selectedProtocolType = type;
        
        // Update tab appearance with enhanced effects
        document.querySelectorAll('.protocol-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-protocol="${type}"]`).classList.add('active');
        
        // Update building interface if open
        if (!document.getElementById('building-interface').classList.contains('hidden')) {
            this.updateBuildingInterface();
        }
        
        // Play tab switch sound
        if (window.audioManager) {
            window.audioManager.playSound('buttonClick', { volume: 0.3 });
        }
    }

    updateBuildingInterface() {
        if (window.phaserGameManager) {
            window.phaserGameManager.updateBuildingInterface();
        }
    }

    confirmBuild() {
        const interface = document.getElementById('building-interface');
        const row = parseInt(interface.dataset.row);
        const col = parseInt(interface.dataset.col);
        const position = `${row}-${col}`;
        
        const result = protocolManager.buildProtocol(this.selectedProtocolType, position);
        
        if (result.success) {
            this.addProtocolToGame(result.protocol);
            this.closeBuildingInterface();
            
            // Enhanced success feedback
            if (window.enhancedUI) {
                window.enhancedUI.showEnhancedNotification(
                    `${protocolManager.protocolTypes[this.selectedProtocolType].name} built successfully!`,
                    'success',
                    { subtitle: 'Your DeFi empire grows stronger!' }
                );
            }
        } else {
            // Enhanced error feedback
            if (window.enhancedUI) {
                window.enhancedUI.showEnhancedNotification(
                    result.reason,
                    'error'
                );
            }
            
            if (window.audioManager) {
                window.audioManager.playError();
            }
        }
    }

    addProtocolToGame(protocol) {
        // Add to Phaser if available
        if (window.phaserGameManager) {
            window.phaserGameManager.addProtocolToGrid(protocol);
        }
        
        this.occupiedCells.add(protocol.position);
        this.updateUI();
    }

    closeBuildingInterface() {
        document.getElementById('building-interface').classList.add('hidden');
    }

    // Enhanced UI Updates
    updateUI() {
        if (window.enhancedUI) {
            window.enhancedUI.updateResourceDisplay();
        }
        this.updateProtocolTabs();
        this.updateMainMenuStats();
    }

    updateProtocolTabs() {
        const playerLevel = gameState.getPlayerLevel();
        const availableProtocols = protocolManager.getAvailableProtocols(playerLevel);
        
        document.querySelectorAll('.protocol-tab').forEach(tab => {
            const protocolType = tab.dataset.protocol;
            if (availableProtocols[protocolType]) {
                tab.style.display = 'flex';
                tab.disabled = false;
                tab.style.opacity = '1';
            } else {
                tab.style.display = 'none';
            }
        });
    }

    updateMainMenuStats() {
        if (this.currentScreen !== 'main-menu') return;
        
        const stats = gameState.data.statistics;
        document.getElementById('best-level').textContent = gameState.data.player.level;
        document.getElementById('total-eth').textContent = StringUtils.formatNumber(stats.totalEthEarned);
        document.getElementById('total-protocols').textContent = stats.protocolsBuilt;
    }

    // Enhanced Game Loop
    startGameLoop() {
        if (this.gameLoopInterval) return;
        
        this.gameLoopInterval = setInterval(() => {
            if (!this.isPaused) {
                this.updateGameLoop();
            }
        }, 100); // 10 FPS for game logic
    }

    stopGameLoop() {
        if (this.gameLoopInterval) {
            clearInterval(this.gameLoopInterval);
            this.gameLoopInterval = null;
        }
    }

    updateGameLoop() {
        // Update daily challenge timer
        if (window.achievementSystem) {
            window.achievementSystem.updateDailyChallengeUI();
        }
        
        // Update resource displays
        this.updateUI();
        
        // Auto-save every 30 seconds
        if (Date.now() - (this.lastAutoSave || 0) > 30000) {
            this.saveGame();
            this.lastAutoSave = Date.now();
        }
    }

    // Enhanced Controls
    togglePause() {
        this.isPaused = !this.isPaused;
        
        const pauseBtn = document.getElementById('pause-btn');
        const icon = pauseBtn.querySelector('.btn-icon');
        
        if (this.isPaused) {
            icon.textContent = '▶️';
            if (window.phaserGameManager) {
                window.phaserGameManager.pauseGame();
            }
            if (window.audioManager) {
                window.audioManager.pauseMusic();
            }
        } else {
            icon.textContent = '⏸️';
            if (window.phaserGameManager) {
                window.phaserGameManager.resumeGame();
            }
            if (window.audioManager) {
                window.audioManager.playMusic();
            }
        }
        
        // Show pause notification
        if (window.enhancedUI) {
            window.enhancedUI.showEnhancedNotification(
                this.isPaused ? 'Game Paused' : 'Game Resumed',
                'info',
                { duration: 1500 }
            );
        }
    }

    toggleSound() {
        if (window.audioManager) {
            const isMuted = window.audioManager.toggleMute();
            const soundBtn = document.getElementById('sound-toggle-btn');
            soundBtn.textContent = isMuted ? '🔇' : '🔊';
            
            // Save audio settings
            window.audioManager.saveAudioSettings();
        }
    }

    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen().catch(err => {
                console.warn('Fullscreen request failed:', err);
            });
        } else {
            document.exitFullscreen();
        }
    }

    onFullscreenChange() {
        const fullscreenBtn = document.getElementById('fullscreen-btn');
        if (fullscreenBtn) {
            fullscreenBtn.textContent = document.fullscreenElement ? '⛶' : '⛶';
        }
    }

    // Enhanced Modal Management
    showAchievements() {
        console.log('Show achievements modal');
        // This would open the achievements modal with enhanced animations
    }

    showLeaderboard() {
        console.log('Show leaderboard modal');
        // This would open the leaderboard modal
    }

    showSettings() {
        console.log('Show settings modal');
        // This would open the settings modal with audio/video options
    }

    showShop() {
        if (window.shopManager) {
            window.shopManager.openShop();
        }
    }

    // Event Handlers
    onResourceChanged(data) {
        // Enhanced UI will handle the visual effects
    }

    onLevelUp(data) {
        // Enhanced UI will handle the visual effects
        this.updateProtocolTabs();
    }

    onProtocolBuilt(protocol) {
        // Enhanced UI and Phaser will handle the visual effects
    }

    onProtocolEarned(data) {
        // Enhanced UI and Phaser will handle the visual effects
    }

    // Save/Load
    saveGame() {
        const success = gameState.save();
        if (success && window.enhancedUI) {
            window.enhancedUI.showEnhancedNotification(
                'Game Saved',
                'success',
                { duration: 1500 }
            );
        }
    }

    // Utility Methods
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Cleanup
    destroy() {
        this.stopGameLoop();
        
        if (window.audioManager) {
            window.audioManager.destroy();
        }
        
        if (window.phaserGameManager) {
            window.phaserGameManager.destroy();
        }
    }
}

// Initialize enhanced game when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Replace the original game instance
    window.game = new EnhancedGame();
});
