/**
 * Enhanced Audio Manager
 * Manages all game audio including music, sound effects, and dynamic audio
 */

class AudioManager {
    constructor() {
        this.sounds = new Map();
        this.music = null;
        this.masterVolume = 0.7;
        this.musicVolume = 0.4;
        this.sfxVolume = 0.6;
        this.isInitialized = false;
        this.isMuted = false;
        
        this.initializeAudio();
    }

    async initializeAudio() {
        try {
            // Initialize Howler.js audio context
            Howler.volume(this.masterVolume);
            
            // Load background music
            this.music = new Howl({
                src: [this.generateTone(220, 4, 'sine'), this.generateTone(330, 4, 'sine')],
                loop: true,
                volume: this.musicVolume,
                autoplay: false,
                onload: () => console.log('Background music loaded'),
                onloaderror: (id, error) => console.warn('Music load error:', error)
            });

            // Load sound effects
            await this.loadSoundEffects();
            
            this.isInitialized = true;
            console.log('Audio Manager initialized successfully');
        } catch (error) {
            console.warn('Audio initialization failed:', error);
            this.isInitialized = false;
        }
    }

    async loadSoundEffects() {
        const soundEffects = {
            // UI Sounds
            buttonClick: { frequency: 800, duration: 0.1, type: 'square' },
            buttonHover: { frequency: 600, duration: 0.05, type: 'sine' },
            modalOpen: { frequency: 400, duration: 0.2, type: 'triangle' },
            modalClose: { frequency: 300, duration: 0.15, type: 'triangle' },
            
            // Game Sounds
            protocolBuild: { frequency: 500, duration: 0.3, type: 'sawtooth' },
            protocolUpgrade: { frequency: 700, duration: 0.25, type: 'square' },
            resourceGain: { frequency: 900, duration: 0.2, type: 'sine' },
            levelUp: { frequency: 1000, duration: 0.5, type: 'triangle' },
            
            // Achievement Sounds
            achievementUnlock: { frequency: 1200, duration: 0.4, type: 'sine' },
            questComplete: { frequency: 800, duration: 0.35, type: 'triangle' },
            
            // Error/Warning Sounds
            error: { frequency: 200, duration: 0.3, type: 'sawtooth' },
            warning: { frequency: 400, duration: 0.2, type: 'square' },
            
            // Ambient Sounds
            energyRecharge: { frequency: 300, duration: 0.8, type: 'sine' },
            protocolHum: { frequency: 150, duration: 2, type: 'sine' },
            
            // Special Effects
            coinDrop: { frequency: 800, duration: 0.15, type: 'triangle' },
            powerUp: { frequency: 1500, duration: 0.3, type: 'sine' },
            explosion: { frequency: 100, duration: 0.4, type: 'sawtooth' }
        };

        for (const [name, config] of Object.entries(soundEffects)) {
            try {
                const audioData = this.generateTone(config.frequency, config.duration, config.type);
                this.sounds.set(name, new Howl({
                    src: [audioData],
                    volume: this.sfxVolume,
                    preload: true
                }));
            } catch (error) {
                console.warn(`Failed to load sound effect: ${name}`, error);
            }
        }
    }

    generateTone(frequency, duration, type = 'sine') {
        const sampleRate = 44100;
        const samples = Math.floor(sampleRate * duration);
        const buffer = new ArrayBuffer(44 + samples * 2);
        const view = new DataView(buffer);
        
        // WAV header
        const writeString = (offset, string) => {
            for (let i = 0; i < string.length; i++) {
                view.setUint8(offset + i, string.charCodeAt(i));
            }
        };
        
        writeString(0, 'RIFF');
        view.setUint32(4, 36 + samples * 2, true);
        writeString(8, 'WAVE');
        writeString(12, 'fmt ');
        view.setUint32(16, 16, true);
        view.setUint16(20, 1, true);
        view.setUint16(22, 1, true);
        view.setUint32(24, sampleRate, true);
        view.setUint32(28, sampleRate * 2, true);
        view.setUint16(32, 2, true);
        view.setUint16(34, 16, true);
        writeString(36, 'data');
        view.setUint32(40, samples * 2, true);
        
        // Generate audio data
        for (let i = 0; i < samples; i++) {
            const t = i / sampleRate;
            let sample = 0;
            
            switch (type) {
                case 'sine':
                    sample = Math.sin(2 * Math.PI * frequency * t);
                    break;
                case 'square':
                    sample = Math.sin(2 * Math.PI * frequency * t) > 0 ? 1 : -1;
                    break;
                case 'triangle':
                    sample = 2 * Math.abs(2 * (frequency * t - Math.floor(frequency * t + 0.5))) - 1;
                    break;
                case 'sawtooth':
                    sample = 2 * (frequency * t - Math.floor(frequency * t + 0.5));
                    break;
            }
            
            // Apply envelope (fade in/out)
            const envelope = Math.min(t * 10, (duration - t) * 10, 1);
            sample *= envelope * 0.3; // Reduce volume
            
            const intSample = Math.max(-32768, Math.min(32767, Math.floor(sample * 32767)));
            view.setInt16(44 + i * 2, intSample, true);
        }
        
        return URL.createObjectURL(new Blob([buffer], { type: 'audio/wav' }));
    }

    // Music Control
    playMusic() {
        if (this.music && this.isInitialized && !this.isMuted) {
            this.music.play();
        }
    }

    pauseMusic() {
        if (this.music) {
            this.music.pause();
        }
    }

    stopMusic() {
        if (this.music) {
            this.music.stop();
        }
    }

    setMusicVolume(volume) {
        this.musicVolume = Math.max(0, Math.min(1, volume));
        if (this.music) {
            this.music.volume(this.musicVolume);
        }
    }

    // Sound Effects
    playSound(soundName, options = {}) {
        if (!this.isInitialized || this.isMuted) return;
        
        const sound = this.sounds.get(soundName);
        if (sound) {
            const volume = options.volume !== undefined ? options.volume : this.sfxVolume;
            const rate = options.rate !== undefined ? options.rate : 1;
            
            sound.volume(volume);
            sound.rate(rate);
            sound.play();
        } else {
            console.warn(`Sound effect not found: ${soundName}`);
        }
    }

    // Specialized sound methods
    playButtonClick() {
        this.playSound('buttonClick');
    }

    playButtonHover() {
        this.playSound('buttonHover', { volume: 0.3 });
    }

    playProtocolBuild() {
        this.playSound('protocolBuild');
        // Add a subtle coin drop sound
        setTimeout(() => this.playSound('coinDrop', { volume: 0.4 }), 100);
    }

    playResourceGain(amount) {
        const volume = Math.min(0.8, 0.2 + (amount / 1000) * 0.6);
        this.playSound('resourceGain', { volume });
    }

    playLevelUp() {
        this.playSound('levelUp');
        // Add celebration sounds
        setTimeout(() => this.playSound('powerUp', { volume: 0.6 }), 200);
        setTimeout(() => this.playSound('achievementUnlock', { volume: 0.4 }), 400);
    }

    playAchievementUnlock() {
        this.playSound('achievementUnlock');
        setTimeout(() => this.playSound('powerUp', { volume: 0.3 }), 150);
    }

    playError() {
        this.playSound('error');
    }

    playWarning() {
        this.playSound('warning');
    }

    // Dynamic audio effects
    playProtocolHum(protocolCount) {
        if (protocolCount > 0) {
            const volume = Math.min(0.2, protocolCount * 0.02);
            this.playSound('protocolHum', { volume, rate: 0.8 });
        }
    }

    playEnergyRecharge() {
        this.playSound('energyRecharge', { volume: 0.3 });
    }

    // Audio context management
    resumeAudioContext() {
        if (Howler.ctx && Howler.ctx.state === 'suspended') {
            Howler.ctx.resume();
        }
    }

    // Volume controls
    setMasterVolume(volume) {
        this.masterVolume = Math.max(0, Math.min(1, volume));
        Howler.volume(this.masterVolume);
    }

    setSFXVolume(volume) {
        this.sfxVolume = Math.max(0, Math.min(1, volume));
        // Update all loaded sound effects
        for (const sound of this.sounds.values()) {
            sound.volume(this.sfxVolume);
        }
    }

    // Mute controls
    toggleMute() {
        this.isMuted = !this.isMuted;
        if (this.isMuted) {
            this.pauseMusic();
            Howler.mute(true);
        } else {
            this.playMusic();
            Howler.mute(false);
        }
        return this.isMuted;
    }

    mute() {
        this.isMuted = true;
        this.pauseMusic();
        Howler.mute(true);
    }

    unmute() {
        this.isMuted = false;
        this.playMusic();
        Howler.mute(false);
    }

    // Audio preferences
    saveAudioSettings() {
        const settings = {
            masterVolume: this.masterVolume,
            musicVolume: this.musicVolume,
            sfxVolume: this.sfxVolume,
            isMuted: this.isMuted
        };
        localStorage.setItem('defi-dynasty-audio', JSON.stringify(settings));
    }

    loadAudioSettings() {
        try {
            const saved = localStorage.getItem('defi-dynasty-audio');
            if (saved) {
                const settings = JSON.parse(saved);
                this.setMasterVolume(settings.masterVolume || 0.7);
                this.setMusicVolume(settings.musicVolume || 0.4);
                this.setSFXVolume(settings.sfxVolume || 0.6);
                if (settings.isMuted) {
                    this.mute();
                }
            }
        } catch (error) {
            console.warn('Failed to load audio settings:', error);
        }
    }

    // Cleanup
    destroy() {
        this.stopMusic();
        for (const sound of this.sounds.values()) {
            sound.unload();
        }
        this.sounds.clear();
        if (this.music) {
            this.music.unload();
        }
    }
}

// Global audio manager instance
window.audioManager = new AudioManager();

// Auto-resume audio context on user interaction
document.addEventListener('click', () => {
    if (window.audioManager) {
        window.audioManager.resumeAudioContext();
    }
}, { once: true });

// Load audio settings on initialization
document.addEventListener('DOMContentLoaded', () => {
    if (window.audioManager) {
        window.audioManager.loadAudioSettings();
    }
});
