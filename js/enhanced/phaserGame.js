/**
 * Phaser.js Game Integration
 * Advanced graphics, animations, and interactive game world
 */

class PhaserGameManager {
    constructor() {
        this.game = null;
        this.scene = null;
        this.protocolSprites = new Map();
        this.particleSystems = new Map();
        this.gridSize = { width: 8, height: 6 };
        this.cellSize = 80;
        this.isInitialized = false;
        
        this.initializePhaser();
    }

    initializePhaser() {
        const config = {
            type: Phaser.AUTO,
            width: 800,
            height: 600,
            parent: 'phaser-game-container',
            canvas: document.getElementById('game-canvas'),
            backgroundColor: 'transparent',
            physics: {
                default: 'arcade',
                arcade: {
                    gravity: { y: 0 },
                    debug: false
                }
            },
            scene: {
                preload: this.preload.bind(this),
                create: this.create.bind(this),
                update: this.update.bind(this)
            }
        };

        this.game = new Phaser.Game(config);
    }

    preload() {
        // Create procedural textures for protocols
        this.createProtocolTextures();
        
        // Create particle textures
        this.createParticleTextures();
        
        // Create grid texture
        this.createGridTexture();
    }

    create() {
        this.scene = this.game.scene.scenes[0];
        
        // Create background grid
        this.createGrid();
        
        // Create particle systems
        this.createParticleSystems();
        
        // Setup input handling
        this.setupInput();
        
        // Create UI elements
        this.createGameUI();
        
        this.isInitialized = true;
        console.log('Phaser game initialized');
    }

    update() {
        // Update particle systems
        this.updateParticles();
        
        // Update protocol animations
        this.updateProtocolAnimations();
        
        // Update resource flows
        this.updateResourceFlows();
    }

    createProtocolTextures() {
        const graphics = this.scene.add.graphics();
        
        // AMM Protocol
        graphics.clear();
        graphics.fillGradientStyle(0x00d4ff, 0x4ecdc4, 0x00a8cc, 0x3ba89a, 1);
        graphics.fillRoundedRect(0, 0, 64, 64, 8);
        graphics.generateTexture('amm-protocol', 64, 64);
        
        // Lending Protocol
        graphics.clear();
        graphics.fillGradientStyle(0x4ecdc4, 0x7ed6d1, 0x3ba89a, 0x6bc4ba, 1);
        graphics.fillRoundedRect(0, 0, 64, 64, 8);
        graphics.generateTexture('lending-protocol', 64, 64);
        
        // Staking Protocol
        graphics.clear();
        graphics.fillGradientStyle(0xffe66d, 0xffed8a, 0xccb856, 0xccbe6e, 1);
        graphics.fillRoundedRect(0, 0, 64, 64, 8);
        graphics.generateTexture('staking-protocol', 64, 64);
        
        // Governance Protocol
        graphics.clear();
        graphics.fillGradientStyle(0xa8e6cf, 0xc4f0db, 0x86b8a5, 0x9cc0b0, 1);
        graphics.fillRoundedRect(0, 0, 64, 64, 8);
        graphics.generateTexture('governance-protocol', 64, 64);
        
        graphics.destroy();
    }

    createParticleTextures() {
        const graphics = this.scene.add.graphics();
        
        // Coin particle
        graphics.clear();
        graphics.fillGradientStyle(0xffd700, 0xffed4e, 0xcc8400, 0xccaa3e, 1);
        graphics.fillCircle(8, 8, 6);
        graphics.generateTexture('coin-particle', 16, 16);
        
        // Energy particle
        graphics.clear();
        graphics.fillGradientStyle(0xff6b6b, 0xff8e8e, 0xcc5555, 0xcc7171, 1);
        graphics.fillCircle(6, 6, 4);
        graphics.generateTexture('energy-particle', 12, 12);
        
        // Token particle
        graphics.clear();
        graphics.fillGradientStyle(0x00d4ff, 0x4ecdc4, 0x00a8cc, 0x3ba89a, 1);
        graphics.fillCircle(5, 5, 3);
        graphics.generateTexture('token-particle', 10, 10);
        
        graphics.destroy();
    }

    createGridTexture() {
        const graphics = this.scene.add.graphics();
        graphics.lineStyle(1, 0x00d4ff, 0.3);
        
        // Draw grid lines
        for (let x = 0; x <= this.gridSize.width; x++) {
            graphics.moveTo(x * this.cellSize, 0);
            graphics.lineTo(x * this.cellSize, this.gridSize.height * this.cellSize);
        }
        
        for (let y = 0; y <= this.gridSize.height; y++) {
            graphics.moveTo(0, y * this.cellSize);
            graphics.lineTo(this.gridSize.width * this.cellSize, y * this.cellSize);
        }
        
        graphics.strokePath();
        graphics.generateTexture('grid', this.gridSize.width * this.cellSize, this.gridSize.height * this.cellSize);
        graphics.destroy();
    }

    createGrid() {
        // Add grid background
        const gridSprite = this.scene.add.image(400, 300, 'grid');
        gridSprite.setAlpha(0.5);
        
        // Create interactive grid cells
        this.gridCells = [];
        for (let row = 0; row < this.gridSize.height; row++) {
            this.gridCells[row] = [];
            for (let col = 0; col < this.gridSize.width; col++) {
                const x = col * this.cellSize + this.cellSize / 2 + 80;
                const y = row * this.cellSize + this.cellSize / 2 + 60;
                
                const cell = this.scene.add.rectangle(x, y, this.cellSize - 4, this.cellSize - 4, 0x000000, 0);
                cell.setInteractive();
                cell.setStrokeStyle(2, 0x00d4ff, 0.3);
                
                cell.on('pointerover', () => {
                    cell.setStrokeStyle(2, 0x00d4ff, 0.8);
                    cell.setFillStyle(0x00d4ff, 0.1);
                });
                
                cell.on('pointerout', () => {
                    cell.setStrokeStyle(2, 0x00d4ff, 0.3);
                    cell.setFillStyle(0x000000, 0);
                });
                
                cell.on('pointerdown', () => {
                    this.onGridCellClick(row, col);
                });
                
                this.gridCells[row][col] = cell;
            }
        }
    }

    createParticleSystems() {
        // Resource gain particles
        this.particleSystems.set('coins', this.scene.add.particles(0, 0, 'coin-particle', {
            speed: { min: 50, max: 150 },
            scale: { start: 0.5, end: 0 },
            lifespan: 1000,
            alpha: { start: 1, end: 0 },
            emitting: false
        }));
        
        this.particleSystems.set('energy', this.scene.add.particles(0, 0, 'energy-particle', {
            speed: { min: 30, max: 100 },
            scale: { start: 0.8, end: 0 },
            lifespan: 800,
            alpha: { start: 1, end: 0 },
            emitting: false
        }));
        
        this.particleSystems.set('tokens', this.scene.add.particles(0, 0, 'token-particle', {
            speed: { min: 40, max: 120 },
            scale: { start: 0.6, end: 0 },
            lifespan: 900,
            alpha: { start: 1, end: 0 },
            emitting: false
        }));
    }

    createGameUI() {
        // Add floating resource indicators
        this.resourceIndicators = {
            eth: this.scene.add.text(50, 50, '1000 ETH', {
                fontSize: '18px',
                fill: '#00d4ff',
                fontFamily: 'Orbitron'
            }),
            tokens: this.scene.add.text(50, 80, '0 Tokens', {
                fontSize: '18px',
                fill: '#ffe66d',
                fontFamily: 'Orbitron'
            }),
            energy: this.scene.add.text(50, 110, '100/100 Energy', {
                fontSize: '18px',
                fill: '#ff6b6b',
                fontFamily: 'Orbitron'
            })
        };
    }

    setupInput() {
        // Handle keyboard shortcuts
        this.scene.input.keyboard.on('keydown-SPACE', () => {
            this.pauseGame();
        });
        
        this.scene.input.keyboard.on('keydown-ESC', () => {
            this.showMainMenu();
        });
    }

    onGridCellClick(row, col) {
        const position = `${row}-${col}`;
        
        // Check if cell is occupied
        if (this.protocolSprites.has(position)) {
            this.showProtocolDetails(position);
            return;
        }
        
        // Show building interface
        this.showBuildingInterface(row, col);
        
        // Play click sound
        if (window.audioManager) {
            window.audioManager.playButtonClick();
        }
    }

    buildProtocol(type, row, col) {
        const position = `${row}-${col}`;
        const x = col * this.cellSize + this.cellSize / 2 + 80;
        const y = row * this.cellSize + this.cellSize / 2 + 60;
        
        // Create protocol sprite
        const sprite = this.scene.add.image(x, y, `${type}-protocol`);
        sprite.setScale(0);
        sprite.setInteractive();
        
        // Add protocol icon
        const iconText = this.getProtocolIcon(type);
        const icon = this.scene.add.text(x, y, iconText, {
            fontSize: '24px',
            fill: '#ffffff'
        }).setOrigin(0.5);
        
        // Animate in
        this.scene.tweens.add({
            targets: [sprite, icon],
            scale: 1,
            duration: 500,
            ease: 'Back.easeOut',
            onComplete: () => {
                this.addProtocolAnimations(sprite, icon, type);
            }
        });
        
        // Store sprite reference
        this.protocolSprites.set(position, { sprite, icon, type });
        
        // Create build particles
        this.createBuildParticles(x, y);
        
        // Play build sound
        if (window.audioManager) {
            window.audioManager.playProtocolBuild();
        }
    }

    addProtocolAnimations(sprite, icon, type) {
        // Idle animation
        this.scene.tweens.add({
            targets: sprite,
            scaleX: 1.05,
            scaleY: 0.95,
            duration: 2000,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });
        
        // Icon rotation
        this.scene.tweens.add({
            targets: icon,
            rotation: Math.PI * 2,
            duration: 10000,
            repeat: -1,
            ease: 'Linear'
        });
        
        // Type-specific animations
        switch (type) {
            case 'amm':
                this.addAMMEffects(sprite);
                break;
            case 'lending':
                this.addLendingEffects(sprite);
                break;
            case 'staking':
                this.addStakingEffects(sprite);
                break;
            case 'governance':
                this.addGovernanceEffects(sprite);
                break;
        }
    }

    addAMMEffects(sprite) {
        // Pulsing glow effect
        this.scene.tweens.add({
            targets: sprite,
            alpha: 0.7,
            duration: 1500,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });
    }

    addLendingEffects(sprite) {
        // Subtle scale animation
        this.scene.tweens.add({
            targets: sprite,
            scale: 1.1,
            duration: 3000,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });
    }

    addStakingEffects(sprite) {
        // Energy pulse effect
        const energyRing = this.scene.add.circle(sprite.x, sprite.y, 40, 0xffe66d, 0);
        energyRing.setStrokeStyle(2, 0xffe66d, 0.5);
        
        this.scene.tweens.add({
            targets: energyRing,
            scale: 1.5,
            alpha: 0,
            duration: 2000,
            repeat: -1,
            ease: 'Quad.easeOut'
        });
    }

    addGovernanceEffects(sprite) {
        // Rotating particles around the protocol
        const particles = this.scene.add.particles(sprite.x, sprite.y, 'token-particle', {
            speed: 20,
            scale: { start: 0.3, end: 0 },
            lifespan: 3000,
            frequency: 500,
            emitZone: { type: 'edge', source: new Phaser.Geom.Circle(0, 0, 35), quantity: 1 }
        });
    }

    createBuildParticles(x, y) {
        const particles = this.particleSystems.get('coins');
        particles.setPosition(x, y);
        particles.explode(10);
    }

    createResourceParticles(type, x, y, amount) {
        const particles = this.particleSystems.get(type);
        if (particles) {
            particles.setPosition(x, y);
            const count = Math.min(20, Math.max(5, Math.floor(amount / 100)));
            particles.explode(count);
        }
    }

    showBuildingInterface(row, col) {
        const interface = document.getElementById('building-interface');
        if (interface) {
            interface.classList.remove('hidden');
            
            // Store current position
            interface.dataset.row = row;
            interface.dataset.col = col;
            
            // Update interface based on selected protocol type
            this.updateBuildingInterface();
        }
    }

    updateBuildingInterface() {
        const interface = document.getElementById('building-interface');
        if (!interface || !window.game) return;
        
        const selectedType = window.game.selectedProtocolType;
        const protocolDef = window.protocolManager.protocolTypes[selectedType];
        
        if (protocolDef) {
            document.getElementById('preview-icon').textContent = protocolDef.icon;
            document.getElementById('preview-name').textContent = protocolDef.name;
            document.getElementById('preview-description').textContent = protocolDef.description;
            document.getElementById('cost-eth').textContent = `${protocolDef.baseCost.eth} ETH`;
            document.getElementById('cost-energy').textContent = `${protocolDef.baseCost.energy} Energy`;
        }
    }

    getProtocolIcon(type) {
        const icons = {
            amm: '🔄',
            lending: '🏦',
            staking: '⚡',
            governance: '🏛️'
        };
        return icons[type] || '❓';
    }

    updateParticles() {
        // Update particle system positions and effects
        // This would be called from the main update loop
    }

    updateProtocolAnimations() {
        // Update protocol-specific animations
        for (const [position, protocolData] of this.protocolSprites) {
            const { sprite, type } = protocolData;
            
            // Add earning effects periodically
            if (Math.random() < 0.01) { // 1% chance per frame
                this.createResourceParticles('coins', sprite.x, sprite.y, 100);
            }
        }
    }

    updateResourceFlows() {
        // Create visual resource flows between protocols
        // This could show connections and resource transfers
    }

    // Public API methods
    addProtocolToGrid(protocol) {
        const [row, col] = protocol.position.split('-').map(Number);
        this.buildProtocol(protocol.type, row, col);
    }

    removeProtocolFromGrid(position) {
        const protocolData = this.protocolSprites.get(position);
        if (protocolData) {
            protocolData.sprite.destroy();
            protocolData.icon.destroy();
            this.protocolSprites.delete(position);
        }
    }

    updateResourceDisplay(resources) {
        if (this.resourceIndicators) {
            this.resourceIndicators.eth.setText(`${resources.eth} ETH`);
            this.resourceIndicators.tokens.setText(`${resources.tokens} Tokens`);
            this.resourceIndicators.energy.setText(`${resources.energy}/${resources.maxEnergy} Energy`);
        }
    }

    showProtocolEarning(position, amount, type) {
        const protocolData = this.protocolSprites.get(position);
        if (protocolData) {
            this.createResourceParticles(type, protocolData.sprite.x, protocolData.sprite.y, amount);
        }
    }

    pauseGame() {
        if (this.scene) {
            this.scene.scene.pause();
        }
    }

    resumeGame() {
        if (this.scene) {
            this.scene.scene.resume();
        }
    }

    showMainMenu() {
        if (window.game) {
            window.game.showScreen('main-menu');
        }
    }

    showProtocolDetails(position) {
        console.log(`Show details for protocol at ${position}`);
        // This would open a detailed protocol management interface
    }

    resize(width, height) {
        if (this.game) {
            this.game.scale.resize(width, height);
        }
    }

    destroy() {
        if (this.game) {
            this.game.destroy(true);
        }
    }
}

// Global Phaser game manager instance
window.phaserGameManager = new PhaserGameManager();

// Handle window resize
window.addEventListener('resize', () => {
    if (window.phaserGameManager) {
        const container = document.getElementById('phaser-game-container');
        if (container) {
            window.phaserGameManager.resize(container.clientWidth, container.clientHeight);
        }
    }
});
