/**
 * Game State Management System
 * Handles all game data, persistence, and state updates
 */

class GameState {
    constructor() {
        this.data = {
            player: {
                level: 1,
                experience: 0,
                experienceToNext: 100,
                name: "DeFi Pioneer"
            },
            resources: {
                eth: 1000,
                tokens: 0,
                energy: 100,
                maxEnergy: 100,
                governance: 0
            },
            protocols: {
                built: [],
                active: [],
                upgrades: {}
            },
            world: {
                current: "ethereum",
                unlocked: ["ethereum"],
                level: 1
            },
            shop: {
                purchases: [],
                premiumItems: []
            },
            achievements: [],
            statistics: {
                totalEthEarned: 0,
                protocolsBuilt: 0,
                transactionsProcessed: 0,
                playTime: 0,
                startTime: Date.now()
            },
            settings: {
                soundEnabled: true,
                musicEnabled: true,
                notifications: true,
                autoSave: true
            },
            tutorial: {
                completed: false,
                currentStep: 0,
                skipped: false
            }
        };

        this.saveKey = 'defi-dynasty-save';
        this.autoSaveInterval = null;
        this.energyRegenInterval = null;
        
        this.load();
        this.startAutoSave();
        this.startEnergyRegen();
    }

    // Resource Management
    addResource(type, amount) {
        if (this.data.resources.hasOwnProperty(type)) {
            this.data.resources[type] += amount;
            this.updateStatistics(type, amount);
            this.checkLevelUp();
            this.triggerEvent('resourceChanged', { type, amount, total: this.data.resources[type] });
            return true;
        }
        return false;
    }

    spendResource(type, amount) {
        if (this.data.resources.hasOwnProperty(type) && this.data.resources[type] >= amount) {
            this.data.resources[type] -= amount;
            this.triggerEvent('resourceChanged', { type, amount: -amount, total: this.data.resources[type] });
            return true;
        }
        return false;
    }

    canAfford(costs) {
        for (const [resource, amount] of Object.entries(costs)) {
            if (!this.data.resources.hasOwnProperty(resource) || this.data.resources[resource] < amount) {
                return false;
            }
        }
        return true;
    }

    spendResources(costs) {
        if (!this.canAfford(costs)) return false;
        
        for (const [resource, amount] of Object.entries(costs)) {
            this.spendResource(resource, amount);
        }
        return true;
    }

    // Protocol Management
    buildProtocol(type, position) {
        const protocolId = `${type}_${Date.now()}`;
        const protocol = {
            id: protocolId,
            type: type,
            position: position,
            level: 1,
            built: Date.now(),
            earnings: 0,
            lastUpdate: Date.now()
        };

        this.data.protocols.built.push(protocol);
        this.data.protocols.active.push(protocolId);
        this.data.statistics.protocolsBuilt++;
        
        this.triggerEvent('protocolBuilt', protocol);
        return protocol;
    }

    upgradeProtocol(protocolId, upgradeType) {
        const protocol = this.data.protocols.built.find(p => p.id === protocolId);
        if (!protocol) return false;

        if (!this.data.protocols.upgrades[protocolId]) {
            this.data.protocols.upgrades[protocolId] = {};
        }

        const currentLevel = this.data.protocols.upgrades[protocolId][upgradeType] || 0;
        this.data.protocols.upgrades[protocolId][upgradeType] = currentLevel + 1;
        
        this.triggerEvent('protocolUpgraded', { protocolId, upgradeType, level: currentLevel + 1 });
        return true;
    }

    // Level and Experience System
    addExperience(amount) {
        this.data.player.experience += amount;
        this.checkLevelUp();
    }

    checkLevelUp() {
        while (this.data.player.experience >= this.data.player.experienceToNext) {
            this.data.player.experience -= this.data.player.experienceToNext;
            this.data.player.level++;
            this.data.player.experienceToNext = Math.floor(this.data.player.experienceToNext * 1.5);
            
            // Level up rewards
            this.addResource('energy', 20);
            this.data.resources.maxEnergy += 10;
            
            this.triggerEvent('levelUp', { 
                level: this.data.player.level, 
                experienceToNext: this.data.player.experienceToNext 
            });
        }
    }

    // World Management
    unlockWorld(worldId) {
        if (!this.data.world.unlocked.includes(worldId)) {
            this.data.world.unlocked.push(worldId);
            this.triggerEvent('worldUnlocked', { worldId });
        }
    }

    switchWorld(worldId) {
        if (this.data.world.unlocked.includes(worldId)) {
            this.data.world.current = worldId;
            this.triggerEvent('worldChanged', { worldId });
            return true;
        }
        return false;
    }

    // Statistics and Achievements
    updateStatistics(type, amount) {
        switch (type) {
            case 'eth':
                if (amount > 0) this.data.statistics.totalEthEarned += amount;
                break;
        }
        
        this.data.statistics.playTime = Date.now() - this.data.statistics.startTime;
        this.checkAchievements();
    }

    checkAchievements() {
        // Achievement checking logic will be implemented here
        // For now, just trigger an event
        this.triggerEvent('achievementsChecked');
    }

    // Energy System
    startEnergyRegen() {
        this.energyRegenInterval = setInterval(() => {
            if (this.data.resources.energy < this.data.resources.maxEnergy) {
                this.addResource('energy', 1);
            }
        }, 30000); // Regenerate 1 energy every 30 seconds
    }

    // Save/Load System
    save() {
        try {
            const saveData = {
                ...this.data,
                saveTime: Date.now(),
                version: "1.0.0"
            };
            localStorage.setItem(this.saveKey, JSON.stringify(saveData));
            this.triggerEvent('gameSaved');
            return true;
        } catch (error) {
            console.error('Failed to save game:', error);
            return false;
        }
    }

    load() {
        try {
            const savedData = localStorage.getItem(this.saveKey);
            if (savedData) {
                const parsed = JSON.parse(savedData);
                
                // Merge saved data with default structure to handle version updates
                this.data = this.mergeDeep(this.data, parsed);
                
                // Update start time for play time calculation
                this.data.statistics.startTime = Date.now() - (this.data.statistics.playTime || 0);
                
                this.triggerEvent('gameLoaded');
                return true;
            }
        } catch (error) {
            console.error('Failed to load game:', error);
        }
        return false;
    }

    startAutoSave() {
        if (this.data.settings.autoSave) {
            this.autoSaveInterval = setInterval(() => {
                this.save();
            }, 60000); // Auto-save every minute
        }
    }

    stopAutoSave() {
        if (this.autoSaveInterval) {
            clearInterval(this.autoSaveInterval);
            this.autoSaveInterval = null;
        }
    }

    // Utility Methods
    mergeDeep(target, source) {
        const output = Object.assign({}, target);
        if (this.isObject(target) && this.isObject(source)) {
            Object.keys(source).forEach(key => {
                if (this.isObject(source[key])) {
                    if (!(key in target))
                        Object.assign(output, { [key]: source[key] });
                    else
                        output[key] = this.mergeDeep(target[key], source[key]);
                } else {
                    Object.assign(output, { [key]: source[key] });
                }
            });
        }
        return output;
    }

    isObject(item) {
        return item && typeof item === 'object' && !Array.isArray(item);
    }

    // Event System
    triggerEvent(eventType, data = {}) {
        const event = new CustomEvent(`game:${eventType}`, { detail: data });
        document.dispatchEvent(event);
    }

    // Getters
    getResource(type) {
        return this.data.resources[type] || 0;
    }

    getProtocols() {
        return this.data.protocols.built;
    }

    getPlayerLevel() {
        return this.data.player.level;
    }

    getCurrentWorld() {
        return this.data.world.current;
    }

    hasSaveData() {
        return localStorage.getItem(this.saveKey) !== null;
    }

    // Reset game
    reset() {
        localStorage.removeItem(this.saveKey);
        location.reload();
    }

    // Cleanup
    destroy() {
        this.stopAutoSave();
        if (this.energyRegenInterval) {
            clearInterval(this.energyRegenInterval);
        }
    }
}

// Global game state instance
window.gameState = new GameState();
