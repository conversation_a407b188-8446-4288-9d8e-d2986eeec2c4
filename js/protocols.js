/**
 * DeFi Protocols System
 * Defines all protocol types, their mechanics, and earning systems
 */

class ProtocolManager {
    constructor() {
        this.protocolTypes = {
            amm: {
                name: "Automated Market Maker",
                icon: "🔄",
                description: "Provides liquidity for token swaps and earns fees",
                baseCost: { eth: 100, energy: 10 },
                baseEarning: 5,
                earningType: "eth",
                unlockLevel: 1,
                maxLevel: 10,
                category: "trading"
            },
            lending: {
                name: "Lending Protocol",
                icon: "🏦",
                description: "Lends assets to users and earns interest",
                baseCost: { eth: 250, energy: 15 },
                baseEarning: 8,
                earningType: "eth",
                unlockLevel: 3,
                maxLevel: 10,
                category: "lending"
            },
            staking: {
                name: "Staking Pool",
                icon: "⚡",
                description: "Stakes tokens and earns rewards",
                baseCost: { eth: 150, tokens: 50, energy: 12 },
                baseEarning: 3,
                earningType: "tokens",
                unlockLevel: 2,
                maxLevel: 10,
                category: "staking"
            },
            governance: {
                name: "DAO Governance",
                icon: "🏛️",
                description: "Manages protocol decisions and earns governance tokens",
                baseCost: { eth: 500, tokens: 100, energy: 25 },
                baseEarning: 2,
                earningType: "governance",
                unlockLevel: 5,
                maxLevel: 10,
                category: "governance"
            },
            derivatives: {
                name: "Derivatives Exchange",
                icon: "📈",
                description: "Trades complex financial instruments",
                baseCost: { eth: 750, tokens: 200, energy: 30 },
                baseEarning: 12,
                earningType: "eth",
                unlockLevel: 7,
                maxLevel: 10,
                category: "trading"
            },
            insurance: {
                name: "DeFi Insurance",
                icon: "🛡️",
                description: "Provides coverage for smart contract risks",
                baseCost: { eth: 400, tokens: 150, energy: 20 },
                baseEarning: 6,
                earningType: "eth",
                unlockLevel: 6,
                maxLevel: 10,
                category: "insurance"
            }
        };

        this.upgradeTypes = {
            efficiency: {
                name: "Efficiency Upgrade",
                description: "Increases earning rate by 25%",
                baseCost: { eth: 50, tokens: 25 },
                maxLevel: 5,
                effect: (level) => 1 + (level * 0.25)
            },
            capacity: {
                name: "Capacity Upgrade",
                description: "Increases maximum liquidity by 50%",
                baseCost: { eth: 75, tokens: 30 },
                maxLevel: 3,
                effect: (level) => 1 + (level * 0.5)
            },
            automation: {
                name: "Automation Upgrade",
                description: "Reduces energy consumption by 20%",
                baseCost: { eth: 100, tokens: 50 },
                maxLevel: 4,
                effect: (level) => Math.max(0.2, 1 - (level * 0.2))
            },
            security: {
                name: "Security Upgrade",
                description: "Reduces risk of exploits and increases user trust",
                baseCost: { eth: 125, tokens: 75 },
                maxLevel: 3,
                effect: (level) => 1 + (level * 0.15)
            }
        };

        this.activeProtocols = new Map();
        this.earningInterval = null;
        this.startEarningSystem();
        
        // Listen for game events
        document.addEventListener('game:protocolBuilt', (e) => this.onProtocolBuilt(e.detail));
        document.addEventListener('game:protocolUpgraded', (e) => this.onProtocolUpgraded(e.detail));
    }

    // Protocol Building
    canBuildProtocol(type, playerLevel) {
        const protocolDef = this.protocolTypes[type];
        if (!protocolDef) return { canBuild: false, reason: "Unknown protocol type" };
        
        if (playerLevel < protocolDef.unlockLevel) {
            return { 
                canBuild: false, 
                reason: `Requires level ${protocolDef.unlockLevel}` 
            };
        }

        if (!gameState.canAfford(protocolDef.baseCost)) {
            return { 
                canBuild: false, 
                reason: "Insufficient resources" 
            };
        }

        return { canBuild: true };
    }

    buildProtocol(type, position) {
        const buildCheck = this.canBuildProtocol(type, gameState.getPlayerLevel());
        if (!buildCheck.canBuild) {
            return { success: false, reason: buildCheck.reason };
        }

        const protocolDef = this.protocolTypes[type];
        if (!gameState.spendResources(protocolDef.baseCost)) {
            return { success: false, reason: "Failed to spend resources" };
        }

        const protocol = gameState.buildProtocol(type, position);
        this.activateProtocol(protocol);
        
        // Award experience for building
        gameState.addExperience(20);

        return { success: true, protocol };
    }

    activateProtocol(protocol) {
        const protocolDef = this.protocolTypes[protocol.type];
        if (!protocolDef) return;

        const activeProtocol = {
            ...protocol,
            definition: protocolDef,
            lastEarning: Date.now(),
            totalEarned: 0,
            efficiency: 1.0,
            capacity: 1.0,
            energyCost: 1.0,
            security: 1.0
        };

        this.activeProtocols.set(protocol.id, activeProtocol);
    }

    // Protocol Upgrades
    canUpgradeProtocol(protocolId, upgradeType) {
        const protocol = this.activeProtocols.get(protocolId);
        if (!protocol) return { canUpgrade: false, reason: "Protocol not found" };

        const upgradeDef = this.upgradeTypes[upgradeType];
        if (!upgradeDef) return { canUpgrade: false, reason: "Unknown upgrade type" };

        const currentLevel = this.getUpgradeLevel(protocolId, upgradeType);
        if (currentLevel >= upgradeDef.maxLevel) {
            return { canUpgrade: false, reason: "Maximum level reached" };
        }

        const cost = this.calculateUpgradeCost(upgradeDef.baseCost, currentLevel);
        if (!gameState.canAfford(cost)) {
            return { canUpgrade: false, reason: "Insufficient resources" };
        }

        return { canUpgrade: true, cost };
    }

    upgradeProtocol(protocolId, upgradeType) {
        const upgradeCheck = this.canUpgradeProtocol(protocolId, upgradeType);
        if (!upgradeCheck.canUpgrade) {
            return { success: false, reason: upgradeCheck.reason };
        }

        if (!gameState.spendResources(upgradeCheck.cost)) {
            return { success: false, reason: "Failed to spend resources" };
        }

        gameState.upgradeProtocol(protocolId, upgradeType);
        this.applyUpgradeEffects(protocolId, upgradeType);
        
        // Award experience for upgrading
        gameState.addExperience(10);

        return { success: true };
    }

    applyUpgradeEffects(protocolId, upgradeType) {
        const protocol = this.activeProtocols.get(protocolId);
        if (!protocol) return;

        const upgradeDef = this.upgradeTypes[upgradeType];
        const level = this.getUpgradeLevel(protocolId, upgradeType);
        const effect = upgradeDef.effect(level);

        switch (upgradeType) {
            case 'efficiency':
                protocol.efficiency = effect;
                break;
            case 'capacity':
                protocol.capacity = effect;
                break;
            case 'automation':
                protocol.energyCost = effect;
                break;
            case 'security':
                protocol.security = effect;
                break;
        }
    }

    // Earning System
    startEarningSystem() {
        this.earningInterval = setInterval(() => {
            this.processEarnings();
        }, 5000); // Process earnings every 5 seconds
    }

    processEarnings() {
        const now = Date.now();
        
        for (const [protocolId, protocol] of this.activeProtocols) {
            const timeSinceLastEarning = now - protocol.lastEarning;
            const earningCycles = Math.floor(timeSinceLastEarning / 5000); // 5 second cycles
            
            if (earningCycles > 0) {
                const baseEarning = protocol.definition.baseEarning;
                const totalEarning = baseEarning * earningCycles * protocol.efficiency * protocol.security;
                const energyCost = Math.ceil(earningCycles * protocol.energyCost);
                
                // Check if we have enough energy
                if (gameState.getResource('energy') >= energyCost) {
                    gameState.spendResource('energy', energyCost);
                    gameState.addResource(protocol.definition.earningType, totalEarning);
                    
                    protocol.totalEarned += totalEarning;
                    protocol.lastEarning = now;
                    
                    // Update statistics
                    gameState.data.statistics.transactionsProcessed += earningCycles;
                    
                    // Trigger earning event
                    document.dispatchEvent(new CustomEvent('game:protocolEarned', {
                        detail: { protocolId, earning: totalEarning, type: protocol.definition.earningType }
                    }));
                }
            }
        }
    }

    // Utility Methods
    getUpgradeLevel(protocolId, upgradeType) {
        const upgrades = gameState.data.protocols.upgrades[protocolId];
        return upgrades ? (upgrades[upgradeType] || 0) : 0;
    }

    calculateUpgradeCost(baseCost, currentLevel) {
        const multiplier = Math.pow(1.5, currentLevel);
        const cost = {};
        
        for (const [resource, amount] of Object.entries(baseCost)) {
            cost[resource] = Math.floor(amount * multiplier);
        }
        
        return cost;
    }

    getProtocolInfo(protocolId) {
        return this.activeProtocols.get(protocolId);
    }

    getAvailableProtocols(playerLevel) {
        const available = {};
        
        for (const [type, definition] of Object.entries(this.protocolTypes)) {
            if (playerLevel >= definition.unlockLevel) {
                available[type] = definition;
            }
        }
        
        return available;
    }

    getProtocolStats(protocolId) {
        const protocol = this.activeProtocols.get(protocolId);
        if (!protocol) return null;

        return {
            totalEarned: protocol.totalEarned,
            efficiency: protocol.efficiency,
            capacity: protocol.capacity,
            energyCost: protocol.energyCost,
            security: protocol.security,
            upgrades: Object.keys(this.upgradeTypes).map(upgradeType => ({
                type: upgradeType,
                level: this.getUpgradeLevel(protocolId, upgradeType),
                maxLevel: this.upgradeTypes[upgradeType].maxLevel
            }))
        };
    }

    // Event Handlers
    onProtocolBuilt(protocol) {
        console.log(`Protocol built: ${protocol.type} at position ${protocol.position}`);
    }

    onProtocolUpgraded(data) {
        console.log(`Protocol ${data.protocolId} upgraded: ${data.upgradeType} to level ${data.level}`);
    }

    // Cleanup
    destroy() {
        if (this.earningInterval) {
            clearInterval(this.earningInterval);
        }
    }
}

// Global protocol manager instance
window.protocolManager = new ProtocolManager();
