/**
 * Utility Functions
 * Common helper functions used throughout the game
 */

// Math utilities
const MathUtils = {
    // Linear interpolation
    lerp(start, end, factor) {
        return start + (end - start) * factor;
    },

    // Clamp value between min and max
    clamp(value, min, max) {
        return Math.min(Math.max(value, min), max);
    },

    // Random integer between min and max (inclusive)
    randomInt(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    },

    // Random float between min and max
    randomFloat(min, max) {
        return Math.random() * (max - min) + min;
    },

    // Calculate compound interest
    compoundInterest(principal, rate, time, compound = 1) {
        return principal * Math.pow(1 + rate / compound, compound * time);
    },

    // Calculate percentage
    percentage(value, total) {
        return total === 0 ? 0 : (value / total) * 100;
    }
};

// String utilities
const StringUtils = {
    // Capitalize first letter
    capitalize(str) {
        return str.charAt(0).toUpperCase() + str.slice(1);
    },

    // Convert camelCase to Title Case
    camelToTitle(str) {
        return str.replace(/([A-Z])/g, ' $1').replace(/^./, (str) => str.toUpperCase());
    },

    // Format large numbers with suffixes
    formatNumber(num, decimals = 1) {
        if (num === 0) return '0';
        
        const suffixes = ['', 'K', 'M', 'B', 'T', 'Q'];
        const tier = Math.log10(Math.abs(num)) / 3 | 0;
        
        if (tier === 0) return Math.floor(num).toString();
        
        const suffix = suffixes[tier];
        const scale = Math.pow(10, tier * 3);
        const scaled = num / scale;
        
        return scaled.toFixed(decimals) + suffix;
    },

    // Format currency
    formatCurrency(amount, currency = 'ETH') {
        return `${this.formatNumber(amount)} ${currency}`;
    },

    // Format time duration
    formatDuration(milliseconds) {
        const seconds = Math.floor(milliseconds / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);

        if (days > 0) return `${days}d ${hours % 24}h`;
        if (hours > 0) return `${hours}h ${minutes % 60}m`;
        if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
        return `${seconds}s`;
    }
};

// DOM utilities
const DOMUtils = {
    // Create element with attributes and content
    createElement(tag, attributes = {}, content = '') {
        const element = document.createElement(tag);
        
        Object.entries(attributes).forEach(([key, value]) => {
            if (key === 'className') {
                element.className = value;
            } else if (key === 'dataset') {
                Object.entries(value).forEach(([dataKey, dataValue]) => {
                    element.dataset[dataKey] = dataValue;
                });
            } else {
                element.setAttribute(key, value);
            }
        });
        
        if (content) {
            element.innerHTML = content;
        }
        
        return element;
    },

    // Add CSS class with animation
    addClass(element, className, duration = 300) {
        element.classList.add(className);
        return new Promise(resolve => {
            setTimeout(resolve, duration);
        });
    },

    // Remove CSS class with animation
    removeClass(element, className, duration = 300) {
        element.classList.remove(className);
        return new Promise(resolve => {
            setTimeout(resolve, duration);
        });
    },

    // Toggle CSS class
    toggleClass(element, className) {
        element.classList.toggle(className);
        return element.classList.contains(className);
    },

    // Smooth scroll to element
    scrollToElement(element, duration = 500) {
        const start = window.pageYOffset;
        const target = element.offsetTop;
        const distance = target - start;
        let startTime = null;

        function animation(currentTime) {
            if (startTime === null) startTime = currentTime;
            const timeElapsed = currentTime - startTime;
            const run = MathUtils.lerp(start, target, timeElapsed / duration);
            window.scrollTo(0, run);
            if (timeElapsed < duration) requestAnimationFrame(animation);
        }

        requestAnimationFrame(animation);
    }
};

// Animation utilities
const AnimationUtils = {
    // Fade in element
    fadeIn(element, duration = 300) {
        element.style.opacity = '0';
        element.style.display = 'block';
        
        return new Promise(resolve => {
            const start = performance.now();
            
            function animate(currentTime) {
                const elapsed = currentTime - start;
                const progress = Math.min(elapsed / duration, 1);
                
                element.style.opacity = progress;
                
                if (progress < 1) {
                    requestAnimationFrame(animate);
                } else {
                    resolve();
                }
            }
            
            requestAnimationFrame(animate);
        });
    },

    // Fade out element
    fadeOut(element, duration = 300) {
        return new Promise(resolve => {
            const start = performance.now();
            const startOpacity = parseFloat(getComputedStyle(element).opacity);
            
            function animate(currentTime) {
                const elapsed = currentTime - start;
                const progress = Math.min(elapsed / duration, 1);
                
                element.style.opacity = startOpacity * (1 - progress);
                
                if (progress < 1) {
                    requestAnimationFrame(animate);
                } else {
                    element.style.display = 'none';
                    resolve();
                }
            }
            
            requestAnimationFrame(animate);
        });
    },

    // Slide in from direction
    slideIn(element, direction = 'left', duration = 300) {
        const transforms = {
            left: 'translateX(-100%)',
            right: 'translateX(100%)',
            up: 'translateY(-100%)',
            down: 'translateY(100%)'
        };

        element.style.transform = transforms[direction];
        element.style.display = 'block';
        
        return new Promise(resolve => {
            const start = performance.now();
            
            function animate(currentTime) {
                const elapsed = currentTime - start;
                const progress = Math.min(elapsed / duration, 1);
                
                const currentTransform = transforms[direction];
                const match = currentTransform.match(/(-?\d+)/);
                const value = match ? parseInt(match[1]) : 0;
                const newValue = value * (1 - progress);
                
                if (direction === 'left' || direction === 'right') {
                    element.style.transform = `translateX(${newValue}%)`;
                } else {
                    element.style.transform = `translateY(${newValue}%)`;
                }
                
                if (progress < 1) {
                    requestAnimationFrame(animate);
                } else {
                    element.style.transform = '';
                    resolve();
                }
            }
            
            requestAnimationFrame(animate);
        });
    },

    // Bounce animation
    bounce(element, intensity = 10, duration = 600) {
        return new Promise(resolve => {
            const start = performance.now();
            const originalTransform = element.style.transform;
            
            function animate(currentTime) {
                const elapsed = currentTime - start;
                const progress = elapsed / duration;
                
                if (progress < 1) {
                    const bounce = Math.sin(progress * Math.PI * 4) * intensity * (1 - progress);
                    element.style.transform = `${originalTransform} translateY(${bounce}px)`;
                    requestAnimationFrame(animate);
                } else {
                    element.style.transform = originalTransform;
                    resolve();
                }
            }
            
            requestAnimationFrame(animate);
        });
    }
};

// Local storage utilities
const StorageUtils = {
    // Set item with expiration
    setItem(key, value, expirationHours = null) {
        const item = {
            value: value,
            timestamp: Date.now(),
            expiration: expirationHours ? Date.now() + (expirationHours * 60 * 60 * 1000) : null
        };
        
        try {
            localStorage.setItem(key, JSON.stringify(item));
            return true;
        } catch (error) {
            console.error('Failed to save to localStorage:', error);
            return false;
        }
    },

    // Get item with expiration check
    getItem(key) {
        try {
            const itemStr = localStorage.getItem(key);
            if (!itemStr) return null;
            
            const item = JSON.parse(itemStr);
            
            // Check expiration
            if (item.expiration && Date.now() > item.expiration) {
                localStorage.removeItem(key);
                return null;
            }
            
            return item.value;
        } catch (error) {
            console.error('Failed to load from localStorage:', error);
            return null;
        }
    },

    // Remove item
    removeItem(key) {
        try {
            localStorage.removeItem(key);
            return true;
        } catch (error) {
            console.error('Failed to remove from localStorage:', error);
            return false;
        }
    },

    // Clear all items with prefix
    clearPrefix(prefix) {
        try {
            const keys = Object.keys(localStorage).filter(key => key.startsWith(prefix));
            keys.forEach(key => localStorage.removeItem(key));
            return true;
        } catch (error) {
            console.error('Failed to clear localStorage:', error);
            return false;
        }
    }
};

// Event utilities
const EventUtils = {
    // Debounce function calls
    debounce(func, wait, immediate = false) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                timeout = null;
                if (!immediate) func(...args);
            };
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func(...args);
        };
    },

    // Throttle function calls
    throttle(func, limit) {
        let inThrottle;
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },

    // Custom event dispatcher
    dispatch(eventName, detail = {}) {
        const event = new CustomEvent(eventName, { detail });
        document.dispatchEvent(event);
    }
};

// Validation utilities
const ValidationUtils = {
    // Validate email
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },

    // Validate number range
    isInRange(value, min, max) {
        return value >= min && value <= max;
    },

    // Validate required fields
    validateRequired(fields) {
        const errors = [];
        
        Object.entries(fields).forEach(([key, value]) => {
            if (!value || (typeof value === 'string' && value.trim() === '')) {
                errors.push(`${StringUtils.camelToTitle(key)} is required`);
            }
        });
        
        return errors;
    }
};

// Export utilities to global scope
window.MathUtils = MathUtils;
window.StringUtils = StringUtils;
window.DOMUtils = DOMUtils;
window.AnimationUtils = AnimationUtils;
window.StorageUtils = StorageUtils;
window.EventUtils = EventUtils;
window.ValidationUtils = ValidationUtils;
