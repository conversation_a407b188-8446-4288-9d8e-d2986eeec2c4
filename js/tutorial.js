/**
 * Tutorial System
 * Guides new players through the game mechanics
 */

class TutorialManager {
    constructor() {
        this.isActive = false;
        this.currentStep = 0;
        this.tutorialSteps = [
            {
                title: "Welcome to DeFi Dynasty!",
                text: "Build and manage your own decentralized financial empire. Let's start with the basics!",
                target: null,
                action: null,
                highlight: null
            },
            {
                title: "Understanding Resources",
                text: "You start with 1000 ETH and 100 Energy. ETH is used to build protocols, while Energy powers their operations.",
                target: ".resources",
                action: null,
                highlight: ".resources"
            },
            {
                title: "Build Your First Protocol",
                text: "Click on any empty grid cell to build your first Automated Market Maker (AMM). This will generate ETH over time.",
                target: ".protocol-grid",
                action: "buildProtocol",
                highlight: ".grid-cell:not(.occupied)"
            },
            {
                title: "Protocol Tabs",
                text: "Use these tabs to select different protocol types. More protocols unlock as you level up!",
                target: ".protocol-tabs",
                action: null,
                highlight: ".protocol-tabs"
            },
            {
                title: "Earning Resources",
                text: "Great! Your AMM is now generating ETH automatically. Watch your resources grow in the top bar.",
                target: ".resources",
                action: "waitForEarnings",
                highlight: "#eth-balance"
            },
            {
                title: "Level Up System",
                text: "Building protocols and earning resources gives you experience. Level up to unlock new content!",
                target: "#player-level",
                action: null,
                highlight: "#player-level"
            },
            {
                title: "The Shop",
                text: "Visit the shop to buy upgrades, boosts, and premium items. Click the shop button to explore!",
                target: "#shop-btn",
                action: null,
                highlight: "#shop-btn"
            },
            {
                title: "Energy Management",
                text: "Protocols consume energy to operate. Energy regenerates over time, or you can buy upgrades to increase capacity.",
                target: "#energy-balance",
                action: null,
                highlight: "#energy-balance"
            },
            {
                title: "Build Your Empire",
                text: "You're ready to build your DeFi empire! Experiment with different protocols and strategies. Good luck!",
                target: null,
                action: "complete",
                highlight: null
            }
        ];

        this.setupEventListeners();
    }

    setupEventListeners() {
        // Tutorial controls
        document.getElementById('tutorial-skip').addEventListener('click', () => this.skipTutorial());
        document.getElementById('tutorial-next').addEventListener('click', () => this.nextStep());
        
        // Listen for game events
        document.addEventListener('game:protocolBuilt', () => this.onProtocolBuilt());
        document.addEventListener('game:protocolEarned', () => this.onProtocolEarned());
        
        // ESC key to skip tutorial
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isActive) {
                this.skipTutorial();
            }
        });
    }

    startTutorial() {
        if (gameState.data.tutorial.completed || gameState.data.tutorial.skipped) {
            return;
        }

        this.isActive = true;
        this.currentStep = gameState.data.tutorial.currentStep || 0;
        
        this.showTutorialOverlay();
        this.showCurrentStep();
        
        console.log('Tutorial started');
    }

    showTutorialOverlay() {
        const overlay = document.getElementById('tutorial-overlay');
        overlay.classList.remove('hidden');
        AnimationUtils.fadeIn(overlay, 300);
    }

    hideTutorialOverlay() {
        const overlay = document.getElementById('tutorial-overlay');
        AnimationUtils.fadeOut(overlay, 300).then(() => {
            overlay.classList.add('hidden');
        });
    }

    showCurrentStep() {
        const step = this.tutorialSteps[this.currentStep];
        if (!step) {
            this.completeTutorial();
            return;
        }

        // Update tutorial content
        document.getElementById('tutorial-title').textContent = step.title;
        document.getElementById('tutorial-text').textContent = step.text;
        
        // Update button text
        const nextBtn = document.getElementById('tutorial-next');
        if (this.currentStep === this.tutorialSteps.length - 1) {
            nextBtn.textContent = 'Finish';
        } else if (step.action) {
            nextBtn.textContent = 'Continue';
            nextBtn.style.display = step.action === 'waitForEarnings' ? 'none' : 'block';
        } else {
            nextBtn.textContent = 'Next';
            nextBtn.style.display = 'block';
        }

        // Handle highlighting
        this.clearHighlights();
        if (step.highlight) {
            this.highlightElement(step.highlight);
        }

        // Handle special actions
        this.handleStepAction(step);
        
        // Save progress
        gameState.data.tutorial.currentStep = this.currentStep;
        gameState.save();
    }

    handleStepAction(step) {
        switch (step.action) {
            case 'buildProtocol':
                this.waitForProtocolBuild();
                break;
            case 'waitForEarnings':
                this.waitForEarnings();
                break;
            case 'complete':
                this.completeTutorial();
                break;
        }
    }

    waitForProtocolBuild() {
        // Disable next button until protocol is built
        document.getElementById('tutorial-next').style.display = 'none';
        
        // Add visual cue to grid cells
        document.querySelectorAll('.grid-cell:not(.occupied)').forEach(cell => {
            cell.classList.add('tutorial-highlight');
            cell.style.animation = 'pulse 2s infinite';
        });
    }

    waitForEarnings() {
        // Wait for first earnings, then auto-advance
        document.getElementById('tutorial-next').style.display = 'none';
        
        // Set up a timer to check for earnings
        const checkEarnings = setInterval(() => {
            if (gameState.getResource('eth') > 1000) {
                clearInterval(checkEarnings);
                setTimeout(() => {
                    this.nextStep();
                }, 2000); // Wait 2 seconds after first earning
            }
        }, 1000);
    }

    nextStep() {
        this.currentStep++;
        
        if (this.currentStep >= this.tutorialSteps.length) {
            this.completeTutorial();
        } else {
            this.showCurrentStep();
        }
    }

    skipTutorial() {
        const confirmSkip = confirm('Are you sure you want to skip the tutorial? You can always restart it from the main menu.');
        
        if (confirmSkip) {
            gameState.data.tutorial.skipped = true;
            gameState.data.tutorial.completed = false;
            gameState.save();
            
            this.endTutorial();
        }
    }

    completeTutorial() {
        gameState.data.tutorial.completed = true;
        gameState.data.tutorial.currentStep = 0;
        gameState.save();
        
        // Award completion bonus
        gameState.addResource('eth', 500);
        gameState.addResource('tokens', 100);
        gameState.addExperience(50);
        
        // Show completion message
        if (window.game) {
            window.game.showNotification('Tutorial completed! Bonus rewards added!', 'success');
        }
        
        this.endTutorial();
    }

    endTutorial() {
        this.isActive = false;
        this.clearHighlights();
        this.hideTutorialOverlay();
        
        console.log('Tutorial ended');
    }

    highlightElement(selector) {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            element.classList.add('tutorial-highlight');
            
            // Create spotlight effect
            const spotlight = document.createElement('div');
            spotlight.className = 'tutorial-spotlight';
            spotlight.style.position = 'fixed';
            spotlight.style.pointerEvents = 'none';
            spotlight.style.zIndex = '9999';
            spotlight.style.border = '3px solid #00d4ff';
            spotlight.style.borderRadius = '8px';
            spotlight.style.boxShadow = '0 0 20px rgba(0, 212, 255, 0.5)';
            spotlight.style.animation = 'pulse 2s infinite';
            
            const rect = element.getBoundingClientRect();
            spotlight.style.left = `${rect.left - 5}px`;
            spotlight.style.top = `${rect.top - 5}px`;
            spotlight.style.width = `${rect.width + 10}px`;
            spotlight.style.height = `${rect.height + 10}px`;
            
            document.body.appendChild(spotlight);
        });
    }

    clearHighlights() {
        // Remove highlight classes
        document.querySelectorAll('.tutorial-highlight').forEach(element => {
            element.classList.remove('tutorial-highlight');
            element.style.animation = '';
        });
        
        // Remove spotlight elements
        document.querySelectorAll('.tutorial-spotlight').forEach(spotlight => {
            spotlight.remove();
        });
    }

    // Event handlers
    onProtocolBuilt() {
        if (this.isActive && this.tutorialSteps[this.currentStep]?.action === 'buildProtocol') {
            // Clear grid highlights
            document.querySelectorAll('.grid-cell').forEach(cell => {
                cell.classList.remove('tutorial-highlight');
                cell.style.animation = '';
            });
            
            // Show next button and advance
            document.getElementById('tutorial-next').style.display = 'block';
            
            // Auto-advance after a short delay
            setTimeout(() => {
                this.nextStep();
            }, 1500);
        }
    }

    onProtocolEarned() {
        if (this.isActive && this.tutorialSteps[this.currentStep]?.action === 'waitForEarnings') {
            // This is handled in waitForEarnings method
        }
    }

    // Public methods for external control
    restartTutorial() {
        gameState.data.tutorial.completed = false;
        gameState.data.tutorial.skipped = false;
        gameState.data.tutorial.currentStep = 0;
        gameState.save();
        
        this.currentStep = 0;
        this.startTutorial();
    }

    isTutorialActive() {
        return this.isActive;
    }

    getCurrentStep() {
        return this.currentStep;
    }

    getTutorialProgress() {
        return {
            current: this.currentStep,
            total: this.tutorialSteps.length,
            percentage: Math.round((this.currentStep / this.tutorialSteps.length) * 100)
        };
    }
}

// Add CSS for tutorial animations
const tutorialStyles = `
    .tutorial-highlight {
        position: relative;
        z-index: 10000;
    }
    
    @keyframes pulse {
        0% { 
            box-shadow: 0 0 0 0 rgba(0, 212, 255, 0.7);
            transform: scale(1);
        }
        50% {
            transform: scale(1.05);
        }
        100% { 
            box-shadow: 0 0 0 20px rgba(0, 212, 255, 0);
            transform: scale(1);
        }
    }
    
    .tutorial-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        z-index: 10000;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .tutorial-content {
        background: linear-gradient(135deg, #1a1a3e, #2d1b69);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 15px;
        padding: 2rem;
        max-width: 500px;
        text-align: center;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
    }
    
    .tutorial-content h3 {
        color: #00d4ff;
        margin-bottom: 1rem;
        font-family: 'Orbitron', monospace;
    }
    
    .tutorial-content p {
        color: #ffffff;
        line-height: 1.6;
        margin-bottom: 2rem;
    }
    
    .tutorial-controls {
        display: flex;
        gap: 1rem;
        justify-content: center;
    }
    
    .tutorial-controls button {
        padding: 0.75rem 1.5rem;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-weight: 500;
        transition: all 0.3s ease;
    }
    
    #tutorial-skip {
        background: rgba(255, 255, 255, 0.1);
        color: #a0a0a0;
    }
    
    #tutorial-next {
        background: linear-gradient(45deg, #00d4ff, #4ecdc4);
        color: #1a1a3e;
    }
    
    .tutorial-controls button:hover {
        transform: translateY(-2px);
    }
`;

// Inject tutorial styles
const styleSheet = document.createElement('style');
styleSheet.textContent = tutorialStyles;
document.head.appendChild(styleSheet);

// Global tutorial manager instance
window.tutorialManager = new TutorialManager();
